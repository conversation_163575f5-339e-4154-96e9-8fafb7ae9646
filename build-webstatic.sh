#!/bin/bash
set -ex

echo "PWD:" `pwd`
echo "system version: " `cat /proc/version`
echo "who am i: " `whoami`

echo "node version: " `node -v`
echo "yarn version: " `yarn -v`
 
export NPM_REGISTRY=http://r.npm.sankuai.com
export NODE_DIST=http://npm.sankuai.com/mirrors/node
yarn config set registry http://r.npm.sankuai.com
yarn global add @nine/nine-cli  --registry=$NPM_REGISTRY --disturl=$NODE_DIST --userconfig=$HOME/.mnpmrc  --cache=$HOME/.cache/mnpm
export PATH="$PATH:`yarn global bin`"
pwd=`pwd`
cd
if [ ! -d ".nine" ]; then
  mkdir .nine
fi
cd .nine
touch .ninerc.yml
nine config set registry http://r.npm.sankuai.com --skip-upgrade
cd $pwd

if [ -n "${AWP_DEPLOY_ENV}" ]; then
	echo '妫€娴嬪埌talos鐜锛岃烦杩囦緷璧栧畨瑁�';
else
	echo '瀹夎椤圭洰渚濊禆...';
  
	yarn --production --registry=$NPM_REGISTRY --disturl=$NODE_DIST --userconfig=$HOME/.mnpmrc  --cache=$HOME/.cache/mnpm
	echo "瀹夎瀹屾垚"
fi

echo "start build "
nine build  --skip-upgrade
echo "build success"
