{% block meta %}


  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="theme-color" content="#000000">
  <!-- 灵犀 start-->
  {# 待优化 检测是否存在 #}
  <meta name="lx:category" content="{{ htmlWebpackPlugin.options.lxConfig.category }}">
  <meta name="lx:appnm" content="{{ htmlWebpackPlugin.options.lxConfig.appnm }}">
  <script type="text/javascript" src="https://s3plus.meituan.net/v1/mss_e6aa2b2c35b3432988a7a61f7ed79d37/h5guard/H5guard.js"></script>
  <script>
      if (window) {
          function ssoGuardGetNum(res) {
              if (res && res.code === 200 && res.data && res.data.num && typeof res.data.num === "string") {
                  if (Object.defineProperty) {
                      Object.defineProperty(window, "__SSOGuardRandomNum__", {
                          value: res.data.num,
                          writable: false,
                          configurable: false
                      })
                  } else { window.__SSOGuardRandomNum__ = res.data.num }
              }
          }
      };
  </script>
  <script type="text/javascript" src="https://ssosv.sankuai.com/sson/web/device/info/script/fast"></script>

{% endblock %}
  <link rel="dns-prefetch" href="//analytics.meituan.net"/>
  <link rel="dns-prefetch" href="//wreport.meituan.net"/>
  <link rel="dns-prefetch" href="//report.meituan.com"/>
{% block link %}
{% endblock %}

  
{% if htmlWebpackPlugin.options.title %}
    <title>{{ htmlWebpackPlugin.options.title }}</title>
  {% else %}
    <title>&#8203;&#8203;&#8203;&#8203;</title>
  {% endif %}
{% block css %}
{% endblock %}

{% block body %}
    <div class="header"></div>
    <div class="page-sidebar"></div>
    <div class="page-content " id="root" style="display:block"></div>
    <script src="//mss.sankuai.com/v1/mss_c4375b35f5cb4e678b5b55a48c40cf9d/waimai-mfe/bundle.js"></script>
  <!-- Cat Start -->
  <script>
    "use strict";!function(u,d){var t="owl",e="_Owl_",n="Owl",r="start",c="error",p="on"+c,f=u[p],h="addEventListener",l="attachEvent",v="isReady",b="dataSet";u[t]=u[t]||function(){try{u[t].q=u[t].q||[];var e=[].slice.call(arguments);e[0]===r?u[n]&&u[n][r]?u[n][r](e[1]):u[t].q.unshift(e):u[t].q.push(e)}catch(e){}},u[e]=u[e]||{preTasks:[],pageData:[],use:function(e,t){this[v]?u[n][e](t):this.preTasks.push({api:e,data:[t]})},run:function(t){if(!(t=this).runned){t.runned=!0,t[b]=[],u[p]=function(){t[v]||t[b].push({type:"jsError",data:arguments}),f&&f.apply(u,arguments)},u[h]&&u[h]("unhandledrejection",function(e){t[v]||t[b].push({type:"jsError",data:[e]})});var e=function(e){!t[v]&&e&&t[b].push({type:"resError",data:[e]})};u[h]?u[h](c,e,!0):u[l]&&u[l](p,e);var n="MutationObserver",r=u[n]||u["WebKit"+n]||u["Moz"+n],a=u.performance||u.WebKitPerformance,s="disableMutaObserver";if(r&&a&&a.now)try{var i=-1,o=u.navigator.userAgent;-1<o.indexOf("compatible")&&-1<o.indexOf("MSIE")?(new RegExp("MSIE (\\d+\\.\\d+);").test(o),i=parseFloat(RegExp.$1)):-1<o.indexOf("Trident")&&-1<o.indexOf("rv:11.0")&&(i=11),-1!==i&&i<=11?t[s]=!0:(t.observer=new r(function(e){t.pageData.push({mutations:e,startTime:a.now()})})).observe(d,{childList:!0,subtree:!0})}catch(e){}else t[s]=!0}}},u[e].runned||u[e].run()}(window,document);
  </script>
  <!-- Cat End -->
  
  {% if htmlWebpackPlugin.options.iGateAppKey %}
    <!--iGate start -->
    <script>
       (function () { window.igate = { set appName(c) { console.log(c); var a = void 0, b = new Date; a || (a = 360); b.setDate(b.getDate() + a); document.cookie = "igateApp=" + escape(c) + (null == a ? "" : ";expires=" + b.toGMTString()) + ";path=/" } } })();
       igate.appName = '{{ htmlWebpackPlugin.options.iGateAppKey }}'
    </script>
    <!-- iGate End -->
  {% endif %}
{% endblock %}

{% block afterSDK %}{% endblock %}
{% block js %}
{% endblock %}