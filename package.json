{"name": "waimai_clue", "version": "0.0.1", "description": "", "private": true, "author": "", "main": "index.js", "scripts": {"build": "bash ./pre-deploy", "build-webstatic": "bash ./build-webstatic.sh", "start": "nine dev", "dev": "nine dev"}, "license": "ISC", "devDependencies": {"@mfe/precommit-eslint": "2.0.2", "@nine/nine-cli": "^0.8.0", "@wmfe/eslint-config-mt": "0.0.2", "babel-eslint": "8.2.1", "eslint": "4.17.0", "eslint-config-airbnb": "16.1.0", "eslint-loader": "^1.5.0", "eslint-plugin-import": "2.8.0", "eslint-plugin-jsx-a11y": "6.0.3", "eslint-plugin-react": "7.6.1", "eslint-plugin-typescript": "^0.14.0"}, "dependencies": {"@ahooksjs/use-url-state": "^3.1.3", "@ant-design/icons": "^4.7.0", "@cs/phone-sdk": "^1.0.21", "@dp/owl": "^1.11.1", "@mfe/bellwether-route": "^1.0.9", "@mfe/cc-api-caller-pc": "^1.1.7", "@mfe/cc-outbound": "^3.0.38-alpha.2", "@mfe/diff-next": "^1.0.16", "@mfe/diff-react": "^0.3.2", "@mfe/lx-event-tracker-sdk": "^0.1.79", "@mtfe/audio-player-next": "2.2.0", "@mtfe/block-sdk": "^1.0.3", "@mtfe/sso-web": "^2.4.1", "@nine/nine-preset-wm-pc": "3.4.8", "@roo/roo": "^1.4.13", "@roo/roo-plus": "^0.6.9", "@wmfe/util_param": "^0.2.1", "ahooks": "^3.0.0-alpha.18", "antd": "5.13.3", "axios": "^0.21.1", "classnames": "^2.3.1", "core-js": "^3.22.3", "cssfilter": "0.0.10", "draft-js": "^0.11.7", "draft-js-export-html": "^1.4.1", "immutable": "^4.0.0-rc.12", "moment": "^2.29.1", "prop-types": "^15.7.2", "query-string": "^7.0.0", "react": "^16.12.0", "react-beautiful-dnd": "^13.0.0", "react-dom": "^16.12.0", "react-redux": "^7.2.2", "react-zmage": "^0.8.5-beta.36", "redux": "^4.0.5", "sass": "^1.81.1", "xss": "^1.0.9"}, "repository": {}, "resolutions": {"webpack": "5.68.0", "webpack-dev-server": "4.7.4", "vue": "2.6.14", "@nine/nine-preset-wm-pc/**/commander": "5.1.0"}}