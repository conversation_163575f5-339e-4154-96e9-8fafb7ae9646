// eslint-disable-next-line import/no-extraneous-dependencies
const webpack = require('webpack');
const nineConfig = require('./nine.json');
const path = require('path');

const severs = {
    mock: 'http://yapi.sankuai.com/thrift/mock/project/34112',
    sl: 'selftest-250617-142207-293',
};

const PUBLIC_PATH = process.env.PUBLIC_PATH || '';

nineConfig.appConfig.webpackCustom = {
    plugins: [
        new webpack.DefinePlugin({
            'process.env': {
                PUBLIC_PATH: JSON.stringify(process.env.PUBLIC_PATH),
                VITE_API_PREFIX: JSON.stringify(process.env.VITE_API_PREFIX),
            },
        }),
    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, 'src/'),
        },
        extensions: ['.js', '.jsx', '.json', '.scss', '.less'],
    },
    module: {
        rules: [
            {
                test: /\.jsx|\.tsx$/i,
                use: [
                    {
                        loader: path.resolve(__dirname, path.resolve('node_modules', '@mfe/lx-event-tracker-sdk/dist/webpackLoader')),
                    },
                ],
            },
        ],
    },
    devServer: {
        port: 3030,
        host: 'localhost',
        client: {
            overlay: false,
        },
        headers: {
            'Access-Control-Allow-Origin': '*',
        },
        proxy: {
            '/xianfu/api/clue/asrResult': {
                target: 'http://waimai-openapi.apigw.test.sankuai.com',
                secure: false,
                changeOrigin: true,
                pathRewrite: {
                    '^/xianfu/api/clue/asrResult': '',
                },
            },
            '/xianfu/api/clue/panda': {
                target: 'http://panda.m.waimai.test.sankuai.com',
                pathRewrite: {
                    '^/xianfu/api/clue/panda': '',
                },
                secure: false,
                changeOrigin: true,
            },

            '/uicomponent/api/controlView/keyInfo/**': {
                target: 'http://igate.waimai.test.sankuai.com/',
                pathRewrite: {
                    '^/api': '',
                },
                secure: false,
                changeOrigin: true,
            },
            '/uicomponent/highseas/cates': {
                target: 'http://panda.m.waimai.test.sankuai.com',
                pathRewrite: {
                    '^/api': '',
                },
                secure: false,
                changeOrigin: true,
            },
            '/api/farmer/decoration/poiConfig/cities': {
                target: 'http://waimai-openapi.apigw.test.sankuai.com',
                pathRewrite: {},
                secure: false,
                changeOrigin: true,
            },
            '/api/farmer': {
                target: 'http://waimai-openapi.apigw.test.sankuai.com',
                pathRewrite: {},
                secure: false,
                changeOrigin: true,
            },
            '/highsea': {
                target: 'http://highsea.waimai.test.sankuai.com',
                pathRewrite: {
                    '^/api': '',
                },
                secure: false,
                changeOrigin: true,
            },
            '/file': {
                target: `http://${severs.sl}-sl-farmer.crm.waimai.test.sankuai.com`,
                secure: false,
                changeOrigin: true,
            },
            '/api/newSign/newSignLead': {
                target: `http://${severs.sl}-sl-waimai-openapi.apigw.test.sankuai.com`,
                secure: false,
                changeOrigin: true,
            },
            '/dovecall-web': {
                target: 'http://dovecallapi.waimai.test.sankuai.com',
                secure: false,
                changeOrigin: true,
            },

            '/oauth/v2/token': {
                target: 'http://auth.ai.test.sankuai.com',
                secure: false,
                changeOrigin: true,
            },
            '/api/getAudioToken': {
                target: 'http://inf-faas.vip.sankuai.com',
                secure: false,
                changeOrigin: true,
            },
            '/api/recognizeSpeechFragment': {
                target: 'http://inf-faas.vip.sankuai.com',
                secure: false,
                changeOrigin: true,
            },
            '/api/asyncRecognizeSpeechFragment': {
                target: 'http://inf-faas.vip.sankuai.com',
                secure: false,
                changeOrigin: true,
            },
            '/asr/v1/sentence_recognize': {
                target: 'http://speechplatform.ai.test.sankuai.com',
                secure: false,
                changeOrigin: true,
            },
            '/xianfu/api/clue/openapi/api/newSign/forecast/user': {
                pathRewrite: {
                    '^/xianfu/api/clue/openapi': '',
                },
                target: 'http://waimai-openapi.apigw.test.sankuai.com',
                changeOrigin: true,
            },
        },
    },

};


if (PUBLIC_PATH) { // 判断PUBLIC_PATH, 如果没有的话，就不做设置，回落到igate部署
    nineConfig.appConfig.outputPath = path.join(__dirname, 'build/page/clue');
    nineConfig.appConfig.entry.map(item => item.filename = `${item.name}.html`);
}

nineConfig.appConfig.webpackChain = function (config) {
    config
        .plugin('mini-css-extract-plugin')
        .tap((args) => {
            args[0].ignoreOrder = true;
            return args;
        });
    const publicPath = `${process.env.PUBLIC_PATH || ''}${process.env.VITE_ROUTER_BASE || ''}`;
    const outputPath = path.resolve(path.resolve(__dirname), `build${process.env.VITE_ROUTER_BASE}`);
    if (PUBLIC_PATH) {
        config.output.publicPath(publicPath);
        config.output.path(outputPath);
    }
};
module.exports = nineConfig;
