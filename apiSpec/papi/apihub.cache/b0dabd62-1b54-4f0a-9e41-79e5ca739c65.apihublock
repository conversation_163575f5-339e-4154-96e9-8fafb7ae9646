{"originInfo": {"info": {"id": "b0dabd62-1b54-4f0a-9e41-79e5ca739c65", "commitId": "270d619a-c11a-4b7f-b308-8922f989bea6", "title": "新签", "baseUrl": "", "description": "", "managers": ["huangfengli02"], "category": {"ai": ["babdd118-b9a1-440d-97f5-d37c33ba9503"]}, "branch": "master"}, "apis": [{"id": "babdd118-b9a1-440d-97f5-d37c33ba9503", "commitId": "436050b4-b3e3-4991-91de-083953c91113", "title": "新签线索AI外呼记录", "description": "", "request": {"template": "/api/newSign/agent/contact/list", "method": "GET", "headers": [], "paths": [], "queries": [{"description": "", "schema": {"type": "number", "format": "long"}, "optional": false, "name": "leadId", "default": "", "example": ""}], "bodyType": "Json"}, "responses": [{"title": "默认返回值", "statusCode": 200, "headers": [], "body": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string", "format": "str"}, "data": {"items": {"type": "object", "properties": {"contactId": {"type": "integer"}, "objectId": {"type": "number", "format": "long"}, "objectType": {"type": "integer"}, "telToken": {"type": "string", "format": "str"}, "contactType": {"type": "integer"}, "bizId": {"type": "integer"}, "routePoint": {"type": "string", "format": "str"}, "submitTime": {"type": "number", "format": "long"}, "startTime": {"type": "number", "format": "long"}, "ringStartTime": {"type": "number", "format": "long"}, "talkingStartTime": {"type": "number", "format": "long"}, "endTime": {"type": "number", "format": "long"}, "ringTimeLen": {"type": "integer"}, "talkingTimeLen": {"type": "integer"}, "rank": {"type": "string", "format": "str"}, "status": {"type": "integer"}, "releaseReason": {"type": "integer"}, "audio": {"type": "string", "format": "str"}, "customId": {"type": "string", "format": "str"}, "releaseReasonMsg": {"type": "string", "format": "str"}, "objectName": {"type": "string", "format": "str"}}, "required": ["contactId", "objectId", "objectType", "telToken", "contactType", "bizId", "routePoint", "submitTime", "startTime", "ringStartTime", "talkingStartTime", "endTime", "ringTimeLen", "talkingTimeLen", "rank", "status", "releaseReason", "audio", "customId", "releaseReasonMsg", "objectName"]}}}, "required": ["code", "message", "data"]}}], "doc": "http://a.sankuai.com/projects/api/view/babdd118-b9a1-440d-97f5-d37c33ba9503"}], "declares": {}}, "apiInfo": {"babdd118-b9a1-440d-97f5-d37c33ba9503": {"projectName": "新签", "fileDir": "./api", "apiId": "babdd118-b9a1-440d-97f5-d37c33ba9503", "apiName": "getApiNewSignAgentContactList", "apiTitle": "新签线索AI外呼记录", "commitId": "436050b4-b3e3-4991-91de-083953c91113", "fileName": "getApiNewSignAgentContactList.ts", "typeFileName": "getApiNewSignAgentContactList.d.ts", "fileSuffix": "ts"}}}