/**
 * 新签体验优化
 * @namespace project_1055Types
 * @time 2025-04-01 14:44:12
 * 该文件由 @mfe/cc-api-caller 自动生成
 */

export type project_1055Types = {
    /**
     * 接口 [变更认领人↗](https://f1-better.sankuai.com/#/home?projectId=1055&api_project_id=83407)的 **请求类型**
     * 更新时间：2025-04-01 16:37:47
     */
    '/api/newSign/manage/changeOwner': {
        method: 'POST';
        request: {
            leadIdList: {}
[];
            newOwnerUid: number;
            targetStatus: number;
        };
        response: string;
    };
    /**
     * 接口 [清除新签线索↗](https://f1-better.sankuai.com/#/home?projectId=1055&api_project_id=83409)的 **请求类型**
     * 更新时间：2025-04-01 16:38:30
     */
    '/api/newSign/manage/delete': {
        method: 'POST';
        request: {
            leadIdList: {}
[];
        };
        response: string;
    };
};
