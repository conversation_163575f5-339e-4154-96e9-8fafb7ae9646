{
    "compilerOptions": {
        "target": "ESNext",
        "useDefineForClassFields": true,
        "types": ["vite/client", "vitest"],
        "lib": ["DOM", "DOM.Iterable", "ESNext"],
        "allowJs": false,
        "skipLibCheck": true,
        "noImplicitAny": false,
        "strictNullChecks": false,

        // Recommended
        "esModuleInterop": true,
        "allowSyntheticDefaultImports": true,

        // `"noImplicitThis": true` is part of `strict`
        // Added again here in case some users decide to disable `strict`.
        // This enables stricter inference for data properties on `this`.
        "noImplicitThis": true,
        "strict": true,
        "forceConsistentCasingInFileNames": true,
        "module": "ESNext",
        "moduleResolution": "Node",
        "resolveJsonModule": true,
        // Required in Vite
        "isolatedModules": true,
        "noEmit": true,
        "jsx": "react-jsx",
        "baseUrl": "./",
        "paths": {
            "@src/*": ["src/*"],
            "~/*": ["src/*"]
        }
    },
    "include": ["src", "./helper.d.ts"],
    "exclude": ["src/__mocks__/**"],
    "references": [{ "path": "./tsconfig.node.json" }]
}
