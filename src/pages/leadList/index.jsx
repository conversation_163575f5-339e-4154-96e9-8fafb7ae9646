/* eslint-disable import/no-unresolved */
/* eslint-disable import/extensions */
import Owl from '@dp/owl';

import '@roo/roo/theme/blue/index.css';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
// import 'antd/dist/antd.css';
import React from 'react';
import '../../assets/styles/index.scss'; // 加载全局css
import LeadList from './leadList';
import './leadList.scss';
import { renderMicroApp } from '../../renderMicroApp';
import '../../utils/sso-web';

// 接入 OWL
Owl.start({
    project: 'com.sankuai.wmmerchantfront.tmk.clue',
    pageUrl: 'leadListMine',
    devMode: window.location.href.indexOf('igate.waimai.test.sankuai.com') > -1,
    resource: {
        sampleApi: 1,
    },
    page: {
        sample: 1,
    },
});

const renderApp = () => {
    renderMicroApp(() => (<React.Fragment>
        <ConfigProvider locale={zhCN}>
            <div style={{ paddingLeft: 20 }}>
                {/* <Different > */}
                <LeadList isMine={false} />
                {/* </Different> */}
            </div>
        </ConfigProvider>
    </React.Fragment>));
};

renderApp();
if (process.env.NODE_ENV === 'development') {
    if (module.hot) {
        module.hot.accept();
    }
}
