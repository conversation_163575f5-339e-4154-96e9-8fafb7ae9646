import React, { useRef, useState, useEffect } from 'react';
import { Modal, Button } from 'antd';

import SelectUser from '../../../../components/selectUser';

function ClaimModal(props) {
    const { visible, onConfirm, onCancel, title } = props;
    const [show, setShow] = useState();
    const [clear, setClear] = useState(0);
    const [uid, setUid] = useState();

    useEffect(() => {
        if (visible) {
            setClear(clear + 1)
            setUid('');
        }

    }, [visible]);

    return (
        <Modal
            title={title || '分配认领人'}
            visible={visible}
            closable={false}
            onOk={() => {
                onConfirm && onConfirm(uid)
            }}
            onCancel={() => {
                onCancel && onCancel();
            }}
            okButtonProps={{ disabled: !uid }}
        >
            <SelectUser
                clear={clear}
                onChange={(uid) => {
                    setUid(uid);
                }}></SelectUser>
        </Modal>
    );
}

export default ClaimModal;
