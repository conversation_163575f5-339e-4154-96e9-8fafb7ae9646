/* eslint-disable react/prop-types */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Table as TableA, message, Tag } from 'antd';
import moment from 'moment';
import { bellwetherLinkParse } from '@mfe/bellwether-route';
import ClaimModal from '../claimModal';
import service from '../../../../common/service';
import { WM_POI_ORDER_STATUS, getUnAssignStatus, AI_FOOLOW_STATUS_MAP } from '../../constant';
import { BizEnum } from '../../../../utils/const';
import { TableAgency } from '@mfe/diff-react';

import './index.scss';
const Table = TableAgency(TableA);
const columns = (activeBizId) => {
    return [
        {
            title: '线索ID',
            dataIndex: 'leadId',
            fixed: 'left',
            width: 120,
        },
        {
            title: '客户ID',
            dataIndex: 'customerId',
            width: 120,
        },
        {
            title: '客户名称',
            dataIndex: 'customerName',
            width: 120,
        },
        {
            title: '客户类型',
            dataIndex: 'customerType',
            width: 120,
        },
        {
            title: '关联门店数量',
            dataIndex: 'customerPoiCount',

            width: 120,
            sorter: true,
        },
        {
            title: '签约人',
            dataIndex: 'signer',
            width: 120,
        },

        {
            title: '门店名称',
            dataIndex: 'leadName',
            fixed: 'left',
            width: 180,
            render(text, record) {
                if (record.firstCallFailedTag) {
                    return (<span>{text}<Tag style={{ marginLeft: 1 }} color="#f50">未通一次</Tag>
                    </span>);
                } else {
                    return text || '-';
                }
            },
        },
        {
            title: '上单状态',
            dataIndex: 'wmPoiOrderStatus',
            width: 200,
            render(status) {
                return WM_POI_ORDER_STATUS[status] || '-';
            },
        },
        {
            title: 'AI跟进状态',
            dataIndex: 'aiStatus',
            width: 150,
            render(status) {
                return AI_FOOLOW_STATUS_MAP[status] || '-';
            },
        },
        {
            title: '门店品类',
            dataIndex: 'categoryName',
            width: 200,
            render(text) {
                return text || '-';
            },
        },
        {
            title: '物理城市',
            dataIndex: 'cityName',
            width: 200,
            render(text) {
                return text || '-';
            },
        },
        {
            title: '渠道来源',
            dataIndex: 'channelSourceDesc',
            width: 200,
            render(text) {
                return text || '-';
            },
        },
        {
            title: '流入时间',
            dataIndex: 'intoTime',
            width: 200,
            sorter: BizEnum.QY !== activeBizId ? true : false,
            render(intoTime) {
                if (intoTime) {
                    return moment(+intoTime * 1000).format('YYYY-MM-DD HH:mm:ss');
                } else {
                    return '-';
                }
            },
        },
        {
            title: '跟进状态',
            dataIndex: 'statusDesc',
            width: 200,
            render(text) {
                return text || '-';
            },
        },
        {
            title: '跟进人',
            dataIndex: 'claimName',
            width: 200,
            render(text, record) {
                if (record.claimMisId) {
                    return `${record.claimName}（${record.claimMisId}）`;
                } else {
                    return '-';
                }
            },
        },
        {
            title: '辅助跟进人',
            dataIndex: 'auxFollowerMis',
            width: 200,
            render(text, record) {
                if (record.auxFollowerMis) {
                    return `${record.auxFollowerName}（${record.auxFollowerMis}）`;
                } else {
                    return '-';
                }
            },
        },
        {
            title: '最新标签',
            dataIndex: 'latestLabelInfo',
            width: 200,
            render(text) {
                if (text) {
                    return text;
                } else {
                    return '-';
                }
            },

        },
        {
            title: '备注',
            dataIndex: 'labelRemarks',
            width: 200,
            render(text) {
                if (text) {
                    return text;
                } else {
                    return '-';
                }
            },
        },

    ]
};
let hasReq = false;
export default function LeadTable(props) {
    const {
        loading, setLoading, onSelect, onPage, onQuery, refresh, data, auth, activeBizId,
    } = props;
    const [pageSize, setPageSize] = useState(10);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [visible, setVisible] = useState(false);

    const leadAuth = useCallback((text) => {

        if (getUnAssignStatus(activeBizId).NUM_VALUE.includes(text) && auth && (auth.uiControl.newsign_assign)) {
            return true;
        } else {
            return false;
        }
    }, [auth]);


    const selLeadId = useRef();
    useEffect(() => {
        setSelectedRowKeys([]);
    }, [refresh]);
    return (
        <div>
            <Table
                diffId='table'
                loading={loading}
                scroll={{ x: 1200 }}
                rowKey="leadId"
                onChange={(pageInfo, filter, sorter) => {
                    const param = {};
                    if (pageInfo && Object.keys(pageInfo).length > 0) {
                        param.pageSize = pageInfo.pageSize;
                        param.pageNo = pageInfo.current;
                    }

                    if (sorter && Object.keys(sorter).length > 0) {
                        param.sortAttribute = sorter.field;
                        param.sortType = sorter.order === 'ascend' ? 1 : 2;
                    }

                    onPage(param);
                }}
                rowSelection={{
                    selectedRowKeys,
                    onChange(keys, allInfo) {
                        setSelectedRowKeys(keys);
                        onSelect && onSelect(allInfo);
                    },
                }}
                columns={columns(activeBizId).concat([{
                    title: '操作',
                    dataIndex: 'operation',
                    align: 'center',
                    fixed: 'right',
                    width: 200,
                    render(text, record) {

                        return (
                            <div>
                                <a href={bellwetherLinkParse(`leadDetail.html?leadId=${record.leadId}&bizId=${activeBizId}`)} target="_blank" className="op">查看详情</a>
                                {
                                    leadAuth(record.status) ? <span
                                        className="op"
                                        onClick={() => {
                                            selLeadId.current = record.leadId;
                                            setVisible(true);
                                        }}
                                    >分配</span> : null
                                }

                                {
                                    getUnAssignStatus(activeBizId).NUM_VALUE.includes(record.status) && auth && auth.uiControl.newsign_claim ? <span
                                        className="op"
                                        onClick={() => {
                                            setLoading(true);
                                            service.post(service.apiMap.claim.path, { leadId: record.leadId }).then((res) => {
                                                setLoading(false);
                                                if (res.code === 0) {
                                                    message.success('认领成功');
                                                    onQuery();
                                                } else {
                                                    message.error(res.msg || '认领失败');
                                                }
                                            }).catch(() => {
                                                setLoading(false);
                                                message.error('认领失败');
                                            });
                                        }}
                                    >认领</span> : null
                                }
                                {
                                    // 已闭环状态下的 认领操作
                                    BizEnum.QY !== activeBizId && [5, 4].includes(record.status) && auth && auth.uiControl.newsign_claim ?
                                        <span
                                            className="op"
                                            onClick={() => {
                                                setLoading(true);
                                                service.post(service.apiMap.loopClaim.path, { leadId: record.leadId }).then((res) => {
                                                    setLoading(false);
                                                    if (res.code === 0) {
                                                        message.success('认领成功');
                                                        onQuery();
                                                    } else {
                                                        message.error(res.msg || '认领失败');
                                                    }
                                                }).catch((e) => {
                                                    setLoading(false);
                                                    message.error(e?.msg || '认领失败');
                                                });
                                            }}
                                        >认领</span> : null
                                }

                            </div>);
                    },
                },
                ])}
                dataSource={data ? data.leadList : []}
                pagination={{
                    pageSizeOptions: [10, 20, 30, 40, 50],
                    showSizeChanger: true,
                    pageSize: data && data.pageSize,
                    current: data && data.pageNo,
                    total: data && data.total,
                    onChange: (pageNo, pageSize) => {
                        setPageSize(pageSize);
                    },
                }}
            />

            <ClaimModal
                ClaimModal
                visible={visible}
                onConfirm={(uid) => {
                    if (hasReq) {
                        return;
                    }
                    hasReq = true;
                    service.post(service.apiMap.assign.path, {
                        leadId: selLeadId.current,
                        uid,
                    }).then((res) => {
                        hasReq = false;
                        if (res.code === 0) {
                            setVisible(false);
                            message.success('分配成功');
                            onQuery();
                        } else {
                            message.error(res.msg || '分配失败');
                        }
                    }).catch((res) => {
                        hasReq = false;
                        message.error(res.msg || '分配失败');
                    });
                }}
                onCancel={() => {
                    setVisible(false);
                }}
            />
        </div >
    );
}

