import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Row, Col, Upload, Select, Button, Modal, message, Spin } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { bellwetherLinkParse } from '@mfe/bellwether-route';
import { SOURCE_STATUS } from '../../../../utils/const';
import service from '../../../../common/service';
import getPrefix from '../../../../common/service/getPrefix';
import './index.scss';

const { Option } = Select;

const useQuerySourceData = (url, params, activeBizId) => {
    const [data, setData] = useState(null);

    useEffect(() => {
        if (activeBizId) {
            service.get(url, { ...params, bizId: activeBizId })
                .then((res) => {
                    if (res.code === 0) {
                        setData(res.data);
                    }
                })
                .catch((err) => {
                    message.error('查询渠道异常');
                });
        }
    }, [params, activeBizId]);

    return [data];
};

// 导入结果查询
const useUploadResult = (url, params) => {
    const [data, setData] = useState(null);
    const [status, setStatus] = useState(null);
    const [reqTime, setReqTime] = useState(0);

    useEffect(() => {
        if (status === 'padding' && reqTime < 1000) {
            service.get(url, params)
                .then((res) => {
                    if (res.code === 0) {
                        if (res.data.status === 1) {
                            setTimeout(() => {
                                setStatus('padding');
                                setReqTime(reqTime + 1);
                            }, 1000);
                        } else if (res.data.status === 2) {
                            setStatus('complete');
                            setData(res.data);
                        }
                    } else {
                        message.error(res.msg || '查询导结果异常');
                    }
                })
                .catch((err) => {
                    message.error('查询导结果异常');
                    setStatus(null);
                });
        }
    }, [status, reqTime]);

    return [data, status, setStatus];
};


function UploadModal(props) {
    const {
        visible, onConfirm, onCancel, activeBizId, channel,
    } = props;
    const [show, setShow] = useState();
    const [resultVisible, setResultVisible] = useState(false);
    const [param, setParam] = useState({});
    const [selSource, setSelSource] = useState();
    const [taskId, setTaskId] = useState(null);
    const [fileList, setFileList] = useState([]);
    // const [source] = useQuerySourceData(service.apiMap.sources.path, null, activeBizId);

    const [data, status, setStatus] = useUploadResult(service.apiMap.importResult.path, { taskId });

    const templateUrl = useMemo(() => {
        if (channel) {
            const _source = channel.find(item => item.code === selSource);
            return _source ? _source.templateUrl : null;
        }
    }, [selSource]);
    useEffect(() => {
        // 清理数据
        setParam({});
        setFileList([]);
        setShow(visible);
    }, [visible]);

    useEffect(() => {
        if (data) {
            onConfirm && onConfirm();
            setResultVisible(true);
        }
    }, [data]);


    return (
        <div className="upload">
            <Modal
                title="导入线索"
                visible={show}
                closable={false}
                maskClosable={false}
                onOk={() => {
                    service.post(service.apiMap.importExcel.path, { ...param, bizId: activeBizId }).then((res) => {
                        if (res.code === 0) {
                            setTaskId(res.data);
                            // 启动轮训
                            setStatus('padding');
                        }
                    });
                }}
                onCancel={() => {
                    onCancel && onCancel();
                }}
                cancelButtonProps={{ disabled: status === 'padding' }}
                okButtonProps={{ disabled: !(param.source && param.downloadKey) || status === 'padding' }}
            >
                <div>
                    <Row className="upload__row">
                        <Col span={6} >
                            渠道来源
                        </Col>
                        <Col span={18} >
                            <Select
                                style={{ width: 200 }}
                                placeholder="请选择渠道"
                                value={param.source}
                                onChange={(value) => {
                                    setParam({ ...param, source: value });
                                    setSelSource(value);
                                }}
                            >
                                {
                                    props.channel && props.channel.filter(item => item.importable).map(item => <Option key={item.code} value={item.code}>{item.name}</Option>)
                                    // source && source.map(item => <Option value={item.code}>{item.name}</Option>)
                                }
                            </Select>
                        </Col>
                    </Row>
                    <Row className="upload__row">
                        <Col span={6}>
                            导入模版
                        </Col>
                        <Col span={18}>
                            {
                                param.source ? <a className="link" href={bellwetherLinkParse(templateUrl)}>点击下载</a> : <span style={{ color: '#666' }}>请选择渠道来源</span>
                            }
                        </Col>
                    </Row>
                    <Row className="upload__row">
                        <Col span={6}>
                            上传文件
                        </Col>
                        <Col span={18} >
                            <Upload
                                fileList={fileList}
                                action={`${getPrefix(service.apiMap.upload.path)}${service.apiMap.upload.path}`}
                                beforeUpload={(res) => {
                                    if (res && res.size > 1024 * 1024 * 5) {
                                        message.error('文件大小不能超过5M');
                                        return false;
                                    }

                                    if (!['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'].includes(res.type)) {
                                        message.error('只能上传后缀为.xls,.xlsx文件');
                                        return false;
                                    }
                                }}
                                accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                                maxCount={1}
                                onRemove={(...args) => {
                                    setParam({ ...param, downloadKey: '' });
                                    setFileList([]);
                                }}
                                onChange={(res, ...args) => {
                                    const { file, fileList } = res;
                                    setFileList(fileList);
                                    if (file.response && file.response.code === 0 && file.status !== 'removed') {
                                        setParam({ ...param, downloadKey: file.response.data });
                                    }
                                }}
                            >
                                <Button icon={<UploadOutlined />}>上传</Button>
                            </Upload>
                        </Col>
                    </Row>
                </div>
            </Modal >

            <Modal
                title="线索导入结果"
                visible={resultVisible}
                width={400}
                closable={false}
                onOk={() => {
                    service.post(service.apiMap.importExcel.path, { ...param, bizId: activeBizId }).then((res) => {
                        if (res.code === 0) {
                            setTaskId(res.data);
                            // 启动轮训
                            setStatus('padding');
                        }
                    });
                }}
                onCancel={() => {
                    onCancel && onCancel();
                }}

                footer={[<Button
                    type="default"
                    onClick={() => {
                        setResultVisible(false);
                    }}
                >关闭</Button>]}
            >
                {
                    data ? <div>

                        <div>本次共成功导入条{data.succeedCount ? data.succeedCount : 0}线索</div>
                        <div>导入失败线索列表：
                            <a className="link" href={bellwetherLinkParse(data.result)}>点击下载</a>
                        </div>
                    </div> : null
                }

            </Modal>
        </div >
    );
}

export default UploadModal;
