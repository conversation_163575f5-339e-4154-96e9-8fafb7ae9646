import React, { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Select, Cascader, Form, Input, Row, Col } from 'antd';
import { PlusCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
import useCates from '../../../../hooks/useCates';
import useCitys from '../../../../hooks/useCitys';
import { BizEnum } from '../../../../utils/const';

const { Option } = Select;
const { Item } = Form;

function CluesForm(props) {
    const [form] = Form.useForm();
    const cates = useCates(false);
    const citys = useCitys();
    const [state, setState] = useState({
        addContactPhone: !!props.data?.contactPhone2, // 编辑页，如果存在第二个联系电话，则默认为true
        categoryData: null,
    });

    // 联系电话添加
    const contactPhoneAdd = () => {
        setState({
            ...state,
            addContactPhone: true,
        });
    };

    // 联系电话减少
    const contactPhoneReduction = () => {
        setState({
            ...state,
            addContactPhone: false,
        });
    };

    // 提交表单
    const tj = (callback, callback2, callback3) => {
        form.validateFields().then((values) => {
            values.categoryData = state.categoryData;
            callback(values);
        }).catch((err) => {
            if (callback2) { callback2(err) }
        }).finally((fin) => {
            if (callback3) { callback3(fin) }
        });
    };

    // 操作表单
    const resetFields = (arr) => {
        if (arr && arr.length) {
            form.resetFields(arr);
        } else {
            form.resetFields(); // 清空全部
        }
    };

    useImperativeHandle(props.refInstance, () => ({
        tj,
        resetFields,
    }));

    return (
        <Form
            form={form}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            autoComplete="off"
            initialValues={props.data}
        >
            {
                !props.isEditor
                    ? <Row>
                        <Col span={21}>
                            <Item
                                name="source"
                                label="渠道来源"
                                rules={[{ required: true, message: '渠道来源不得为空' }]}
                            >
                                <Select placeholder="请选择渠道来源">
                                    {
                                        props.channel && props.channel.filter(item => item.creatable).map(item => <Option key={item.code} value={item.code}>{item.name}</Option>)
                                    }
                                </Select>
                            </Item>
                        </Col>
                    </Row>
                    : null
            }
            <Row>
                <Col span={21}>
                    <Item
                        name="leadName"
                        label="门店名称"
                        rules={[
                            { required: true, message: '门店名称不得为空' },
                            { max: 15, message: '最多15个字' },
                        ]}
                    >
                        <Input placeholder="请输入门店名称" maxLength={30} />
                    </Item>
                </Col>
            </Row>
            <Row>
                <Col span={21}>
                    <Item
                        name="categoryId"
                        label="品类"
                        rules={[{ required: true, message: '品类不得为空' }]}
                    >
                        <Cascader
                            style={{ width: '100%' }}
                            showSearch={{
                                filter: (inputValue, path) => path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1),
                            }}
                            options={cates}
                            onChange={(val) => {
                                let da = null;
                                if (val) {
                                    const _val = [...val];
                                    const pop = _val.pop();// 移除最后一个数组元素
                                    if (pop === -1) {
                                        da = _val.length === 0 ? null : _val.pop();
                                    } else {
                                        da = pop;
                                    }
                                }
                                setState({
                                    ...state,
                                    categoryData: da,
                                });
                                // console.log('=', state.categoryData);
                            }}
                            placeholder="请选择品类"
                        />
                    </Item>
                </Col>
            </Row> {
                !props.isEditor
                    ? <Row>
                        <Col span={21}>
                            <Item
                                name="cityId"
                                label="物理城市"
                                rules={[{ required: true, message: '物理城市不得为空' }]}
                            >
                                <Select
                                    showSearch
                                    allowClear
                                    placeholder="请选择物理城市"
                                    optionFilterProp="label"
                                    style={{ width: '100%' }}
                                    onChange={() => { }}
                                    filterOption={(input, option) => {
                                        const value = String(option.value).toLowerCase();
                                        const label = String(option.children).toLowerCase();
                                        const pinyin = String(option.pinyin).toLowerCase();
                                        return (
                                            value.includes(input) || label.includes(input) || pinyin.includes(input)
                                        );
                                    }}
                                >
                                    {
                                        citys && citys.map(item => <Option key={item.cityId} value={item.cityId} pinyin={item.cityPinyin}>{item.cityName}</Option>)
                                    }
                                </Select>
                            </Item>
                        </Col>
                    </Row>
                    : null
            }
            <Row>
                <Col span={21}>
                    <Item
                        name="address"
                        label="门店地址"
                        rules={[
                            { required: true, message: '门店地址不得为空' },
                            { max: 40, message: '最多40个字' },
                        ]}
                    >
                        <Input placeholder="请输入门店地址" maxLength={70} />
                    </Item>
                </Col>
            </Row>
            <Row>
                <Col span={21}>
                    <Item
                        name="contactPhone"
                        label="联系电话"
                        rules={[
                            { required: true, message: '联系电话不得为空' },
                            { pattern: /^[\d-]{11,12}$|^\d{7,8}$|^1{1}\d{10}$/, message: '电话号码位数有误' },
                        ]}
                    >
                        <Input placeholder="请输入联系电话" maxLength={20} />
                    </Item>
                </Col>
                <Col span={3}>
                    <div className="contactPhone-div-add" >
                        {
                            !state.addContactPhone
                                ? <PlusCircleOutlined // 加号
                                    style={{ fontSize: 18 }}
                                    onClick={contactPhoneAdd}
                                />
                                : null
                        }
                    </div>
                </Col>
            </Row>
            {
                state.addContactPhone
                    ? <Row>
                        <Col span={21}>
                            <Item
                                className="contactPhone-name2"
                                colon={false}
                                name="contactPhone2"
                                label=" "
                                rules={[
                                    { required: true, message: '联系电话不得为空' },
                                    { pattern: /^[\d-]{11,12}$|^\d{7,8}$|^1{1}\d{10}$/, message: '电话号码位数有误' },
                                ]}
                            >
                                <Input placeholder="请输入联系电话" maxLength={20} />
                            </Item>
                        </Col>
                        <Col span={3}>
                            <div className="contactPhone-div-add" >
                                <MinusCircleOutlined // 减号
                                    style={{ fontSize: 18 }}
                                    onClick={contactPhoneReduction}
                                />
                            </div>
                        </Col>
                    </Row>
                    : null
            }

        </Form>

    );
}

CluesForm.propTypes = {
    refInstance: PropTypes.object.isRequired,
    data: PropTypes.object,
    isEditor: PropTypes.bool,
    activeBizId: PropTypes.string,
    channel: PropTypes.array,
};
CluesForm.defaultProps = {
    data: {},
    isEditor: false, // 是否是编辑
    activeBizId: 0,
    channel: [],
};

export default forwardRef((props, ref) => <CluesForm {...props} refInstance={ref} />);
