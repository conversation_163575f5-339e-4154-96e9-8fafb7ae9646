import React, { createRef } from 'react';
import { Modal, message } from 'antd';
import PropTypes from 'prop-types';
import { bellwetherLinkParse } from '@mfe/bellwether-route';
import service from '../../../../common/service';
import { apiMap } from '../../../../common/service/apiMap';
import CluesForm from './cluesForm';
import './index.scss';

export default function index({
    visible, onCancel, onConfirm, activeBizId, channel,
}) {
    const cluesRef = createRef();

    // 提交
    const submitValue = () => {
        cluesRef.current.tj((data) => {
            const {
                source, leadName, categoryData, cityId, address, contactPhone, contactPhone2,
            } = data;
            const contentPhones = contactPhone2 ? [contactPhone, contactPhone2] : [contactPhone];
            const request = {
                source, // 线索来源
                leadName, // 门店名称
                categoryId: categoryData, // 三级品类ID
                cityId, // 二级物理城市ID
                address, // 门店地址
                contentPhones, // 联系电话
                bizId: activeBizId,
            };
            service.post(apiMap.addNewClues.path, { ...request }).then((res) => {
                if (res.code === 0) {
                    setTimeout(() => {
                        message.success('新建成功');
                        window.location.href = bellwetherLinkParse(`leadDetail.html?leadId=${res.data}&bizId=${activeBizId}`);
                    }, 500);
                }
            }).catch(() => {
                setTimeout(() => {
                    message.error('新建失败');
                    cluesRef.current.resetFields();
                }, 500);
            }).finally(() => {
                // 关闭弹框
                onConfirm();
            });
        });
    };

    // 取消
    const cancel = () => {
        // 清空表单
        // form.resetFields(['categoryId', 'cityId', 'contactPhone', 'contactPhone2', 'source', 'address', 'leadName']);
        cluesRef.current.resetFields();
        // 关闭弹框
        onCancel();
    };

    return (
        <Modal
            title="新建线索"
            visible={visible}
            onOk={submitValue}
            onCancel={cancel}
            okText="保存"
        >
            <CluesForm
                ref={cluesRef}
                channel={channel}
                activeBizId={activeBizId}
            />
        </Modal >
    );
}

index.propTypes = {
    visible: PropTypes.bool.isRequired,
    onCancel: PropTypes.func.isRequired,
    onConfirm: PropTypes.func.isRequired,
};
index.defaultProps = {
};
