/* eslint-disable no-unused-expressions */
/* eslint-disable react/prop-types */
import React, { useState } from 'react';
import { Tooltip } from '@roo/roo';


function PopItem(props) {
    const { item, onChange, activeKey } = props;
    const [label, setLabel] = useState();
    const activeKeys = ['-1'];

    return (
        <Tooltip
            placement="bottom"
            effect="light"
            trigger="hover"
            content={
                <div style={{ maxHeight: 500, overflow: 'auto' }}>
                    {
                        item.children && item.children.map((child) => {
                            activeKeys.push(child.value);
                            return (
                                <div
                                    className="pop__item"
                                    onClick={() => {
                                        setLabel(child.label);
                                        onChange && onChange(-1, child.value);
                                    }}
                                >
                                    {child.label}</div>
                            );
                        })
                    }
                </div>}
        >
            <div

                className={`${activeKeys.includes(activeKey) ? 'head__item--active' : ''} head__item`}
                onClick={() => {
                    onChange && onChange('-1');
                }}
            >{`${label || item.label} (${item.count})`}</div>
        </Tooltip>
    );
}

export default PopItem;
