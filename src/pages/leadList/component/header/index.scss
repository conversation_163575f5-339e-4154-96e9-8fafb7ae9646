.headList__head {
    margin-top: 20px;
    display: flex;
}

.head__item {
    padding: 12px 0px;
    background: #fafafa;
    cursor: pointer;
    width: 200px;
    text-align: center;
    position: relative;
}

.head__item:before {
    position: absolute;
    top: 50%;
    right: 0;
    width: 1px;
    height: 1.6em;
    background-color: rgba(0, 0, 0, 0.06);
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    transition: background-color 0.3s;
    content: '';
}

.head__item:hover {
    background: #d7edfe;
}

.head__item--active {
    background: #d7edfe;
    color: #2196f3;
}

.pop__item {
    padding: 6px 10px;
    cursor: pointer;
}

.pop__item:hover {
    background: #2196f3;
    color: white;
}

.roo-tooltip-light .roo-tooltip-inner {
    padding: 0;
}

.pop__item {
    width: 200px;
}