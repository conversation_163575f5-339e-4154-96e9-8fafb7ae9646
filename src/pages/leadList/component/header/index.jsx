/* eslint-disable no-unused-expressions */
/* eslint-disable react/prop-types */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import PopItem from './popItem';
import { BizEnum } from '../../../../utils/const';
import service from '../../../../common/service';
import './index.scss';


function Header(props) {
    const {
        onChange, items, activeKey, refresh, params, activeBizId, channel,
    } = props;
    const [active, setActive] = useState('-1');
    const [count, setCount] = useState({});
    const [channelSources, setChannelSources] = useState(0);

    useEffect(() => {
        setActive(activeKey);
    }, [activeKey]);

    useEffect(() => {
        const param = {
            ...params,
        };

        if (channelSources && +channelSources !== 0) {
            param.channelSources = [channelSources];
        }
        if (activeBizId) {
            if (activeBizId === BizEnum.QY) {
                service.post(service.apiMap.qyCount.path, { ...param, bizId: activeBizId }).then((res) => {
                    if (res.code === 0) {
                        setCount(res.data);
                    }
                });
            } else {
                service.post(service.apiMap.count.path, { ...param, bizId: activeBizId }).then((res) => {
                    if (res.code === 0) {
                        setCount(res.data);
                    }
                });
            }
        }
    }, [channelSources, activeBizId, refresh]);

    const itemsMemo = useMemo(() => {
        Object.keys(count).forEach((per) => {
            items.forEach((item) => {
                if (item.countField === per) {
                    item.count = count[per] || 0;
                }
            });
        });
        return items;
    }, [count, items]);

    return (
        <div className="headList__head">

            {
                itemsMemo && itemsMemo.map((item) => {
                    if (item.status === '-1') {
                        return (
                            <PopItem
                                item={item}
                                onChange={(key, childKey) => {
                                    setActive(key);
                                    if (childKey) {
                                        setChannelSources(childKey);
                                    }

                                    onChange && onChange(key, childKey);
                                }}
                                activeKey={active}
                            />);
                    }
                    return (
                        <div
                            className={`${active === item.status ? 'head__item--active' : ''} head__item`}
                            onClick={() => {
                                setActive(item.status);
                                onChange && onChange(item.status);
                            }}
                        >{item.label}({item.count})</div>);
                })
            }
        </div>
    );
}

export default Header;
