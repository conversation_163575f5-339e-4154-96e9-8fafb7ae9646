/* eslint-disable no-console */
/* eslint-disable quotes */
/* eslint-disable react/jsx-indent */
/* eslint-disable indent */
import React, { useState, useEffect, useRef, Fragment } from 'react';
import moment from 'moment';
import { getUrlParam } from '@wmfe/util_param';
import { Button } from '@roo/roo';
import {
    Cascader,
    Select,
    DatePicker,
    Input,
    ConfigProvider,
    message,
    Spin,
    Form,
    Row,
    Col,
    Modal,
    Descriptions,
} from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import PropTypes from 'prop-types';
import { bellwetherLinkParse } from '@mfe/bellwether-route';

import InputValidate from '../../components/inputValidate';
import ReAssignButton from '../../components/lead/ReAssignButton';
import BatchCleanButton from '../../components/lead/BatchCleanButton';
import SelectUser from '../../components/selectUser';
import ClaimModal from './component/claimModal';
import UploadModal from './component/uploadModal';
import NewCluesModal from './component/newCluesModal';
import OrganizationSelectorWrap from '../../components/OrganizationPickerWrap';

import {
    CLOSE_LOOP_TYPE,
    HEAD_LABEL,
    HEAD_LABEL_SG,
    HEAD_LABEL_QY,
    BizEnum,
} from '../../utils/const';
import {
    WM_POI_ORDER_STATUS_LIST,
    getUnAssignStatus,
    AI_FOOLOW_STATUS_LIST,
} from './constant';
import { authCreate } from '../../utils/utils';
import Header from './component/header/index';
import LeadTable from './component/leadTable';
import service from '../../common/service';
import useCates from '../../hooks/useCates';
import useCitys from '../../hooks/useCitys';
import useLabel from '../../hooks/useLabel';
import useCitysTopLevel from '../../hooks/useCitysTopLevel';
import { DifferentItem, FormItemAgency, Different } from '@mfe/diff-react';
import { getDiffConfig } from '../../diff/different';
import '@roo/roo-plus/theme/default/index.css';
import { NewSignTargetStatus } from '../../types';

const Item = FormItemAgency(Form, {
    consumeSet: {
        props: {
            targetDepth: 3,
        },
    },
    wrap(children) {
        return <Col span={6}>{children}</Col>;
    },
});

const { Option } = Select;
const { RangePicker } = DatePicker;
const bizId = getUrlParam('bizId') || '';
// header source
const headerLabel = {
    [`${BizEnum.WM}`]: HEAD_LABEL,
    [`${BizEnum.SG}`]: HEAD_LABEL_SG,
    [`${BizEnum.QY}`]: HEAD_LABEL_QY,
};

const useQueryData = (url, isMine, activeBizId) => {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(false);

    const [param, setParam] = useState({
        pageSize: 10,
        pageNo: 1,
        mine: isMine,
        allTab: true,
        // statusList: ALL_COUNT_STATUS,
        statusList: [],
    });

    useEffect(() => {
        if (activeBizId) {
            setLoading(true);
            let _param = {
                sortType: 2,
                sortAttribute: 'intoTime',
                ...param,
            };

            service
                .post(url, { ..._param, bizId: activeBizId })
                .then(res => {
                    setLoading(false);
                    setData(res.data);
                })
                .catch(() => {
                    setLoading(false);
                    message.error('线索查询异常');
                });
        }
    }, [param, activeBizId]);

    return [data, loading, param, setParam];
};

const useOptionInfoData = (url, activeBizId) => {
    const [data, setData] = useState(null);

    useEffect(() => {
        if (activeBizId) {
            service
                .get(`${url}?bizId=${activeBizId}`)
                .then(res => {
                    setData(res.data);
                })
                .catch(() => {
                    message.error('线索查询异常');
                });
        }
    }, [activeBizId]);

    return [data];
};

const useUserInfoData = url => {
    const [user, setUser] = useState(null);
    useEffect(() => {
        service
            .get(url)
            .then(res => {
                if (res.code === 0 && res.data) {
                    setUser(JSON.parse(res.data));
                }
            })
            .catch(() => {
                message.error('用户获取异常');
            });
    }, []);

    return [user];
};

const useBizManage = () => {
    const [list, setList] = useState([]);
    const [activeBizId, setActiveBizId] = useState('');
    const [auth, setAuth] = useState(null);
    useEffect(() => {
        service
            .get('/api/newSign/newSignLead/auth/getRole')
            .then(res => {
                if (res.code === 0 && res.data) {
                    let newList =
                        (res.data || []).filter(item => item.hasPermission) ||
                        [];
                    if (newList.length === 0) {
                        message.error('请先配置业务线');
                        return;
                    }

                    // 特殊逻辑 后面估计新签不会怎么维护了。乱了乱了
                    const qyTenant = newList.find(
                        item => item.tenantId === BizEnum.QY,
                    );
                    if (qyTenant) {
                        qyTenant.roleMap = {
                            ...qyTenant.roleMap,
                            is_admin: qyTenant.roleMap.is_enterprise_admin,
                            is_business:
                                qyTenant.roleMap.is_enterprise_business,
                        };
                    }
                    setList(newList);

                    let biz = null;
                    if (bizId) {
                        biz = newList.find(item => item.tenantId === +bizId);
                        if (!biz) {
                            message.error('业务线权限异常');
                            return;
                        }
                    } else {
                        [biz] = newList;
                    }

                    setActiveBizId(biz.tenantId);
                    setAuth(authCreate(biz.roleMap));
                }
            })
            .catch(() => {
                message.error('业务线获取异常');
            });
    }, []);
    // activeBizId的类型错误，实际为number
    return [list, activeBizId, auth];
};

const useAreaConfig = (url, activeBizId) => {
    const [areas, setAreas] = useState(null);
    useEffect(() => {
        if (activeBizId && BizEnum.QY !== activeBizId) {
            service
                .get(`${url}?bizId=${activeBizId}`)
                .then(res => {
                    if (res.code === 0 && res.data) {
                        const areasList = Object.entries(
                            JSON.parse(res.data),
                        ).map(item => ({
                            label: item[0],
                            value: item[1],
                        }));
                        areasList.unshift({ label: '全部', value: -1 });
                        setAreas(areasList);
                    } else {
                        message.error(res?.msg || '获取失败');
                    }
                })
                .catch(err => {
                    console.log('/newSignLead/common/getAreaConfig', err);
                });
        }
    }, [activeBizId]);

    return [areas];
};

const useChannel = (url, activeBizId) => {
    const [channel, setChannel] = useState(null);
    useEffect(() => {
        if (activeBizId) {
            service
                .get(`${url}?bizId=${activeBizId}`)
                .then(res => {
                    if (res.code === 0 && res.data) {
                        setChannel(res.data);
                    } else {
                        message.error(res?.msg || '获取失败');
                    }
                })
                .catch(err => {
                    console.log('/newSignLead/common/channelSourceInfo', err);
                });
        }
    }, [activeBizId]);

    return [channel];
};

const getHeadLabel = (defaultLabel, dynamicChannel) => {
    if (dynamicChannel && dynamicChannel.length > 0) {
        const allCount = defaultLabel.find(
            item => item.countField === 'allCount',
        );
        if (allCount) {
            allCount.children = dynamicChannel.map(channel => ({
                label: channel.name,
                value: channel.code,
            }));
        }
    }
    return defaultLabel;
};

const isInnerItem = (ele, isTarget, level = 2) => {
    if (level < 0) {
        return false;
    }
    if (isTarget(ele)) {
        return true;
    }
    return isInnerItem(ele.parentNode, isTarget, level - 1);
};

let hasReqClaim = false;
export default function LeadList(props) {
    const { isMine } = props;
    const [form] = Form.useForm();
    const cates = useCates();
    const citys = useCitys();
    const citysInfo = useCitysTopLevel();

    const [bizList, activeBizId, auth] = useBizManage(); // 业务线状态
    const latestLabelData2 = useLabel(2, activeBizId);
    const latestLabelData3 = useLabel(3, activeBizId);
    const latestLabelData4 = useLabel(4, activeBizId);
    const searchParams = useRef({});
    const selectKeys = useRef([]);
    const [selectKeyState, setSelectKeyState] = useState([]);

    const [statusList, setStatusList] = useState('-1');
    const [visible, setVisible] = useState(false);
    const [uploadVisible, setUploadVisible] = useState(false);
    const [opLoading, setOpLoading] = useState(false);
    const [newClues, setNewClues] = useState(false);
    const [optionInfo] = useOptionInfoData(
        service.apiMap.optionInfo.path,
        activeBizId,
    );

    const [user] = useUserInfoData(service.apiMap.getCurrentUser.path);
    const [data, loading, param, setParam] = useQueryData(
        service.apiMap.getLeadList.path,
        isMine,
        activeBizId,
    );
    const [areas] = useAreaConfig(
        service.apiMap.getAreaConfig.path,
        activeBizId,
    );

    const [channel] = useChannel(
        service.apiMap.channelSourceInfo.path,
        activeBizId,
    );

    // 刷新逻辑, 批量分配后需要刷新header数据以及去除list选中状态
    const [refresh, setRefresh] = useState();
    const getCascaderValue = val => {
        if (val) {
            const _list = [];
            val.forEach(item => {
                if (item !== -1) {
                    _list.push(item);
                }
            });
            return _list.pop();
        } else {
            return null;
        }
    };
    useEffect(() => {
        form.setFieldsValue({
            statusList: '-1',
        });
    }, []);

    const queryList = () => {
        const values = form.getFieldsValue();
        const formParam = {
            ...values,
        };
        const {
            labelTime,
            categoryId,
            intoTime,
            hopeCity,
            statusList,
            lastLabelIds,
            orgIds,
        } = values;
        if (orgIds) {
            formParam.orgIds = orgIds.join(',');
        }
        if (statusList) {
            formParam.statusList =
                statusList === '-1' ? [] : statusList.split(',');
        }
        if (labelTime && labelTime.length === 2) {
            formParam.labelStartTime =
                parseInt(labelTime[0].valueOf() / 1000, 10) || '';
            formParam.labelEndTime =
                parseInt(labelTime[1].valueOf() / 1000, 10) || '';
        } else {
            formParam.labelStartTime = '';
            formParam.labelEndTime = '';
        }
        delete formParam.labelTime;

        if (intoTime && intoTime.length === 2) {
            formParam.intoStartTime =
                parseInt(intoTime[0].valueOf() / 1000, 10) || '';
            formParam.intoEndTime =
                parseInt(intoTime[1].valueOf() / 1000, 10) || '';
        } else {
            formParam.intoStartTime = '';
            formParam.intoEndTime = '';
        }

        // 处理标签 等于-1的时候，表示选择当前层级全部
        if (lastLabelIds && lastLabelIds.includes(-1)) {
            formParam.lastLabelIds = [...lastLabelIds].slice(0, -1);
        }

        delete formParam.intoTime;

        if (categoryId) {
            formParam.categoryId = getCascaderValue(categoryId);
        }

        if (hopeCity) {
            const newValue = [];
            hopeCity.forEach(item => {
                if (item.length === 2) {
                    newValue.push(item.slice(-1));
                } else {
                    newValue.push(item);
                }
            });
            formParam.hopeCity = newValue;
        }

        setParam({ ...param, ...formParam, ...searchParams.current });
        setRefresh(new Date().getTime());
    };
    return (
        <Different data={getDiffConfig(activeBizId)}>
            <div className="leadList">
                <Spin spinning={opLoading}>
                    <Header
                        refresh={refresh}
                        items={getHeadLabel(
                            headerLabel[`${activeBizId}`] || HEAD_LABEL,
                            channel,
                        )}
                        activeKey={statusList}
                        params={{ ...param }}
                        activeBizId={activeBizId}
                        channel={channel}
                        onChange={(active, childKey) => {
                            console.log('active', active);
                            setStatusList(active);

                            // 状态为全部时，不传statusList
                            form.setFieldValue('statusList', `${active}`);
                            searchParams.current.allTab = active === '-1';
                            searchParams.current.statusList =
                                active === '-1' ? [] : `${active}`.split(',');

                            if (childKey) {
                                searchParams.current.channelSourceList =
                                    +childKey === 0 ? null : [childKey];
                            }
                            searchParams.current.endType = undefined;
                            form.setFieldValue('endType', undefined);

                            // 清楚标签信息
                            form.resetFields(['lastLabelIds']);
                            setParam({
                                ...param,
                                ...searchParams.current,
                                pageNo: 1,
                                pageSize: 10,
                            });
                        }}
                    />
                    <div className="leadList__biz">
                        <Select
                            style={{ width: 150 }}
                            options={bizList.map(item => ({
                                label: item.tenantName,
                                value: item.tenantId,
                            }))}
                            onChange={value => {
                                if (
                                    window.location.href.indexOf(
                                        'leadListMine',
                                    ) > -1
                                ) {
                                    window.location.href = bellwetherLinkParse(
                                        `leadListMine.html?bizId=${value}`,
                                    );
                                } else if (
                                    window.location.href.indexOf('leadList') >
                                    -1
                                ) {
                                    window.location.href = bellwetherLinkParse(
                                        `leadList.html?bizId=${value}`,
                                    );
                                }
                            }}
                            value={activeBizId}
                        />
                    </div>
                    <div className="shadow">
                        <Form
                            form={form}
                            labelCol={{ span: 8 }}
                            wrapperCol={{ span: 16 }}
                            onValuesChange={v => {
                                console.log('object', v);
                            }}
                        >
                            <Row style={{ margin: 0 }}>
                                <Item name="leadId" label="线索ID">
                                    <InputValidate placeholder="请输入线索id" />
                                </Item>
                                <Item label="客户ID" name="customerId">
                                    <InputValidate placeholder="请输入" />
                                </Item>
                                <Item label="客户名称" name="customerName">
                                    <Input placeholder="请输入" />
                                </Item>
                                <Item label="客户类型" name="customerType">
                                    <Select
                                        placeholder="请选择客户类型"
                                        defaultValue={null}
                                        options={[
                                            { label: '全部', value: null },
                                        ].concat(
                                            (
                                                (optionInfo &&
                                                    optionInfo.customerType) ||
                                                []
                                            ).map(item => ({
                                                label: item.name,
                                                value: item.code,
                                            })),
                                        )}
                                    />
                                </Item>
                                <Item label="线索名称" name="leadName">
                                    <Input placeholder="关键词模糊匹配" />
                                </Item>
                                <Item label="组织架构" name="orgIds">
                                    <OrganizationSelectorWrap />
                                </Item>
                                <Item label="物理城市" name="cityIds">
                                    <Select
                                        showSearch
                                        allowClear
                                        mode="multiple"
                                        placeholder="请选择物理城市"
                                        optionFilterProp="label"
                                        style={{ width: '100%' }}
                                        filterOption={(input, option) => {
                                            const value = String(
                                                option.value,
                                            ).toLowerCase();
                                            const label = String(
                                                option.children,
                                            ).toLowerCase();
                                            const pinyin = String(
                                                option.pinyin,
                                            ).toLowerCase();
                                            return (
                                                value.includes(input) ||
                                                label.includes(input) ||
                                                pinyin.includes(input)
                                            );
                                        }}
                                    >
                                        {citys &&
                                            citys.map(item => (
                                                <Option
                                                    key={item.cityId}
                                                    value={item.cityId}
                                                    pinyin={item.cityPinyin}
                                                >
                                                    {item.cityName}
                                                </Option>
                                            ))}
                                    </Select>
                                </Item>
                                <Item label="签约人" name="signer">
                                    <Input placeholder="请输入" />
                                </Item>
                                <Item label="打标时间段" name="labelTime">
                                    <RangePicker
                                        showTime
                                        format="YYYY/MM/DD HH:mm:ss"
                                    />
                                </Item>
                                <Item label="跟进状态" name="statusList">
                                    <Select
                                        showSearch
                                        style={{ width: '100%' }}
                                        onChange={val => {
                                            searchParams.current.endType = null;
                                            searchParams.current.statusList =
                                                val === '-1'
                                                    ? []
                                                    : `${val}`.split(',');
                                            setStatusList(val);
                                            queryList();
                                        }}
                                    ></Select>
                                </Item>
                                <Item label="门店品类" name="categoryId">
                                    <Cascader
                                        style={{ width: '100%' }}
                                        showSearch={{
                                            filter: (inputValue, path) =>
                                                path.some(
                                                    option =>
                                                        option.label
                                                            .toLowerCase()
                                                            .indexOf(
                                                                inputValue.toLowerCase(),
                                                            ) > -1,
                                                ),
                                        }}
                                        options={cates}
                                        placeholder="请选择品类"
                                    />
                                </Item>
                                <Item label="联系电话" name="phoneNumber">
                                    <Input placeholder="请输入联系电话" />
                                </Item>
                                <Item label="流入时间段" name="intoTime">
                                    <RangePicker
                                        showTime
                                        format="YYYY/MM/DD HH:mm:ss"
                                    />
                                </Item>

                                {!isMine ? (
                                    <Item label="跟进人" name="claimUid">
                                        <SelectUser
                                            limitUser={
                                                auth &&
                                                    (auth.is_hq || auth.is_admin)
                                                    ? null
                                                    : user
                                            }
                                        />
                                    </Item>
                                ) : null}
                                <Item label="辅助跟进人" name="auxFollowerUid">
                                    <SelectUser />
                                </Item>
                                {`${statusList}` === '4,5' ? (
                                    <Item label="闭环类型" name="endType">
                                        <Select
                                            showSearch
                                            defaultValue={0}
                                            style={{ width: '100%' }}
                                            onChange={v => {
                                                searchParams.current.endType = v;
                                                form.setFieldValue(
                                                    'lastLabelIds',
                                                    [],
                                                );
                                            }}
                                        >
                                            {CLOSE_LOOP_TYPE &&
                                                [{ label: '全部', value: 0 }]
                                                    .concat(CLOSE_LOOP_TYPE)
                                                    .map(item => (
                                                        <Option
                                                            key={item.value}
                                                            value={item.value}
                                                        >
                                                            {item.label}
                                                        </Option>
                                                    ))}
                                        </Select>
                                    </Item>
                                ) : null}
                                {['2', '3', '4,5'].includes(`${statusList}`) ? (
                                    <Form.Item
                                        dependencies={['endType']}
                                        noStyle
                                    >
                                        {({ getFieldValue }) => {
                                            const statusList = `${getFieldValue(
                                                'statusList',
                                            )}`;
                                            const endType = getFieldValue(
                                                'endType',
                                            );
                                            let options = [];
                                            if (statusList === '2') {
                                                options = latestLabelData2;
                                            } else if (statusList === '3') {
                                                options = latestLabelData3;
                                            } else if (statusList === '4,5') {
                                                options = latestLabelData4.filter(
                                                    item => {
                                                        if (endType === 1) {
                                                            return [
                                                                -1,
                                                                3,
                                                            ].includes(
                                                                item.value,
                                                            );
                                                        } else if (
                                                            endType === 2
                                                        ) {
                                                            return [
                                                                -1,
                                                                2,
                                                            ].includes(
                                                                item.value,
                                                            );
                                                        }
                                                        return true;
                                                    },
                                                );
                                            }
                                            return (
                                                <Item
                                                    label="最新标签"
                                                    name="lastLabelIds"
                                                >
                                                    <Cascader
                                                        style={{
                                                            width: '100%',
                                                        }}
                                                        showSearch={{
                                                            filter: (
                                                                inputValue,
                                                                path,
                                                            ) =>
                                                                path.some(
                                                                    option =>
                                                                        option.label
                                                                            .toLowerCase()
                                                                            .indexOf(
                                                                                inputValue.toLowerCase(),
                                                                            ) >
                                                                        -1,
                                                                ),
                                                        }}
                                                        options={options}
                                                        placeholder="请选择最新标签"
                                                    />
                                                </Item>
                                            );
                                        }}
                                    </Form.Item>
                                ) : null}
                                {// 总部人员可查看
                                    auth && auth.is_hq && (
                                        <Item label="外包商" name="orgId">
                                            <Select
                                                placeholder="请选择外包商"
                                                defaultValue={-1}
                                                options={areas}
                                            />
                                        </Item>
                                    )}
                                <Item label="上单状态" name="orderStatus">
                                    <Select
                                        placeholder="请选择上单状态"
                                        defaultValue={null}
                                        options={[
                                            { label: '全部', value: null },
                                        ].concat(WM_POI_ORDER_STATUS_LIST)}
                                    />
                                </Item>
                                <Item label="AI跟进状态" name="aiStatusList">
                                    <Select
                                        placeholder="请选择AI跟进状态"
                                        options={AI_FOOLOW_STATUS_LIST}
                                        allowClear
                                        mode='multiple'
                                        maxTagCount={1}
                                    />
                                </Item>
                                <Item label="KP职级" name="kpLevel">
                                    <Select
                                        placeholder="请选择KP职级"
                                        defaultValue={null}
                                        options={[
                                            { label: '全部', value: null },
                                        ].concat(
                                            (
                                                (optionInfo &&
                                                    optionInfo.kpLevel) ||
                                                []
                                            ).map(item => ({
                                                label: item.name,
                                                value: item.code,
                                            })),
                                        )}
                                    />
                                </Item>
                                <Item label="企业规模" name="enterpriseScale">
                                    <Select
                                        placeholder="请选择企业规模"
                                        defaultValue={null}
                                        options={[
                                            { label: '全部', value: null },
                                        ].concat(
                                            (
                                                (optionInfo &&
                                                    optionInfo.enterpriseScale) ||
                                                []
                                            ).map(item => ({
                                                label: item.name,
                                                value: item.code,
                                            })),
                                        )}
                                    />
                                </Item>
                                <Item
                                    label="3个月内开店规划"
                                    name="openingPlanning"
                                >
                                    <Select
                                        placeholder="请选择3个月内开店规划"
                                        defaultValue={null}
                                        options={[
                                            { label: '全部', value: null },
                                        ].concat(
                                            (
                                                (optionInfo &&
                                                    optionInfo.openingPlanning) ||
                                                []
                                            ).map(item => ({
                                                label: item.name,
                                                value: item.code,
                                            })),
                                        )}
                                    />
                                </Item>
                                <Item
                                    label="意向合作业务"
                                    name="cooperationDemand"
                                >
                                    <Select
                                        placeholder="请选择意向合作业务"
                                        defaultValue={null}
                                        onChange={value => {
                                            searchParams.current.cooperationDemand = value;
                                        }}
                                        options={[
                                            { label: '全部', value: null },
                                        ].concat(
                                            (
                                                (optionInfo &&
                                                    optionInfo.cooperationDemand) ||
                                                []
                                            ).map(item => ({
                                                label: item.name,
                                                value: item.code,
                                            })),
                                        )}
                                    />
                                </Item>
                                <Item label="预期开店城市" name="hopeCity">
                                    <Cascader
                                        multiple
                                        placeholder="请选择预期开店城市"
                                        defaultValue={null}
                                        showSearch={(inputValue, path) =>
                                            path.some(
                                                option =>
                                                    option.label
                                                        .toLowerCase()
                                                        .indexOf(
                                                            inputValue.toLowerCase(),
                                                        ) > -1,
                                            )
                                        }
                                        fieldNames={{
                                            label: 'name',
                                            value: 'code',
                                            children: 'children',
                                        }}
                                        options={citysInfo || []}
                                    />
                                </Item>
                                <Item label="可投资金额" name="incomeRange">
                                    <Select
                                        placeholder="请选择可投资金额"
                                        defaultValue={null}
                                        options={[
                                            { label: '全部', value: null },
                                        ].concat(
                                            (
                                                (optionInfo &&
                                                    optionInfo.incomeRange) ||
                                                []
                                            ).map(item => ({
                                                label: item.name,
                                                value: item.code,
                                            })),
                                        )}
                                    />
                                </Item>
                            </Row>
                        </Form>
                    </div>
                    <div className="leadList__op">
                        {isMine && activeBizId === BizEnum.WM ? (
                            <Button
                                className="leadList__button"
                                onClick={() => {
                                    setNewClues(true);
                                }}
                            >
                                新建
                            </Button>
                        ) : null}
                        {auth && auth.uiControl.newsign_search ? (
                            <Button
                                lx-mc="查询按钮"
                                className="leadList__button"
                                onClick={() => {
                                    queryList();
                                }}
                            >
                                查询
                            </Button>
                        ) : null}
                        <Button
                            className="leadList__button"
                            onClick={() => {
                                form.resetFields();
                                searchParams.current.endType = null;
                                searchParams.current.statusList = [];
                                setStatusList('-1');
                                setRefresh(new Date().getTime());
                                queryList();
                            }}
                        >
                            重置
                        </Button>
                        {auth && auth.uiControl.newsign_import ? (
                            <DifferentItem diffId="clueImportButton">
                                <Button
                                    className="leadList__button"
                                    onClick={() => {
                                        setUploadVisible(true);
                                    }}
                                >
                                    导入
                                </Button>
                            </DifferentItem>
                        ) : null}
                        {auth && auth.uiControl.newsign_export ? (
                            <Button
                                className="leadList__button"
                                onClick={() => {
                                    setOpLoading(true);
                                    service
                                        .post(service.apiMap.export.path, {
                                            ...param,
                                            ...searchParams.current,
                                            bizId: activeBizId,
                                        })
                                        .then(res => {
                                            setOpLoading(false);

                                            if (res.code === 0) {
                                                message.success(
                                                    res.data || '导出成功',
                                                );
                                            } else {
                                                message.error('导出失败');
                                            }
                                        })
                                        .catch(() => {
                                            setOpLoading(false);
                                        });
                                }}
                            >
                                导出
                            </Button>
                        ) : null}
                        {auth && auth.uiControl.newsign_batch_assgin ? (
                            <Button
                                className="leadList__button"
                                onClick={() => {
                                    if (selectKeys.current.length === 0) {
                                        message.error('请选择线索');
                                        return;
                                    }

                                    if (BizEnum.QY === activeBizId) {
                                        setVisible(true);
                                        return;
                                    }

                                    if (
                                        selectKeys.current.find(
                                            item =>
                                                !(
                                                    getUnAssignStatus(
                                                        activeBizId,
                                                    ).STR_VALUE.indexOf(
                                                        `${item.status}`,
                                                    ) > -1
                                                ),
                                        )
                                    ) {
                                        message.error(
                                            '已分配的线索无法操作分配，请重新选择后再次提交',
                                        );
                                    } else {
                                        setVisible(true);
                                    }
                                }}
                            >
                                批量分配
                            </Button>
                        ) : null}
                        {auth &&
                            auth.uiControl.newsign_batch_assgin &&
                            [
                                NewSignTargetStatus.WAIT_FIRST_FOLLOW,
                                NewSignTargetStatus.WATI_SECOND_FOLLOW,
                                NewSignTargetStatus.WATI_THIRD_FOLLOW,
                                NewSignTargetStatus.SUCCESS_CLOSED_LOOP,
                                NewSignTargetStatus.FAILURE_CLOSED_LOOP,
                            ].some(s =>
                                String(statusList)
                                    .split(',')
                                    .map(Number)
                                    .includes(s),
                            ) ? (
                            <ReAssignButton
                                ids={selectKeyState}
                                onReload={() => {
                                    queryList();
                                    setSelectKeyState([]);
                                }}
                            />
                        ) : null}
                        {auth &&
                            auth.uiControl.newsign_clean &&
                            statusList === NewSignTargetStatus.TO_ASSIGN ? (
                            <BatchCleanButton
                                ids={selectKeyState}
                                onReload={() => {
                                    queryList();
                                    setSelectKeyState([]);
                                }}
                            />
                        ) : null}
                    </div>
                    <div className="leadList__table">
                        <ConfigProvider locale={zhCN}>
                            <LeadTable
                                refresh={refresh}
                                setLoading={setOpLoading}
                                data={data}
                                auth={auth}
                                loading={loading}
                                activeBizId={activeBizId}
                                onSelect={keys => {
                                    selectKeys.current = keys;
                                    setSelectKeyState(
                                        keys.map(it => it.leadId),
                                    );
                                }}
                                onPage={page => {
                                    setParam({ ...param, ...page });
                                }}
                                onQuery={() => {
                                    setTimeout(() => {
                                        setParam({ ...param });
                                        setRefresh(new Date().getTime());
                                    }, 1000);
                                }}
                            />
                        </ConfigProvider>
                    </div>
                    <ClaimModal
                        title="批量分配认领人"
                        visible={visible}
                        onConfirm={uid => {
                            if (hasReqClaim) {
                                return;
                            }
                            hasReqClaim = true;
                            service
                                .post(service.apiMap.batchAssign.path, {
                                    leadIdList: selectKeys.current.map(
                                        item => item.leadId,
                                    ),
                                    uid,
                                    bizId: activeBizId,
                                })
                                .then(res => {
                                    hasReqClaim = false;
                                    if (res.code === 0) {
                                        setVisible(false);
                                        setTimeout(() => {
                                            setParam({ ...param });
                                            setRefresh(new Date().getTime());
                                        }, 1000);

                                        selectKeys.current = [];
                                        message.info(res.data || '分配成功');
                                    } else {
                                        message.error(res.msg || '分配失败');
                                    }
                                })
                                .catch(res => {
                                    hasReqClaim = false;
                                    message.error(res.msg || '分配失败');
                                });
                        }}
                        onCancel={() => {
                            setVisible(false);
                        }}
                    />
                    <UploadModal
                        visible={uploadVisible}
                        activeBizId={activeBizId}
                        channel={channel}
                        onCancel={() => {
                            setUploadVisible(false);
                        }}
                        onConfirm={() => {
                            setTimeout(() => {
                                setParam({ ...param });
                                setRefresh(new Date().getTime());
                            }, 1000);
                            setUploadVisible(false);
                        }}
                    />
                    <NewCluesModal
                        visible={newClues}
                        channel={channel}
                        activeBizId={activeBizId}
                        onCancel={() => {
                            setNewClues(false);
                        }}
                        onConfirm={() => {
                            setNewClues(false);
                            setParam({ ...param, ...searchParams.current });
                            setRefresh(new Date().getTime());
                        }}
                    />
                </Spin>
            </div>
        </Different>
    );
}

LeadList.propTypes = {
    isMine: PropTypes.bool,
};
LeadList.defaultProps = {
    isMine: false,
};
