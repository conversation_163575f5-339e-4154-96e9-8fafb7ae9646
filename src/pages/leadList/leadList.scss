.shadow {
    box-shadow: 0 0 6px 0 #e9eaf2;
    padding: 20px 20px 0;
    margin: 20px 20px 20px 0;

    .varityCol {
        div:nth-child(4n+0) {
            flex: 0 0 33.3333%;
            max-width: 33.3333%;
        }
    }
}

.leadList__table {
    width: 100%;
    padding: 10px 20px 10px 0;
}

.leadList__button {
    height: 40px;
    cursor: pointer;
    background: #00abe4;
    border-color: #00abe4;
    align-self: flex-end;
    margin-left: 20px;
}

.leadList {
    // padding-left: 20px;
    margin-top: 5px;
    min-width: 1024px;
    position: relative;
}

.leadList__op {
    text-align: right;
    padding: 0px 20px;
}

.leadList__biz {
    position: absolute;
    display: flex;
    justify-content: flex-end;
    right: 20px;
    top: -35px;
    width: 200px;
}

.ant-breadcrumb ol {
    padding-inline-start: 0px !important;
    margin-bottom: 0;
}

.lead-detail-tx-blue {
    color: #3C99D8;
}