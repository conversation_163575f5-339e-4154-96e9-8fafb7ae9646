import { BizEnum } from "../../utils/const";

const WM_POI_ORDER_STATUS = ['下线', '上线', '上单中', '审核通过待上线'];
const WM_POI_ORDER_STATUS_LIST = [
    {
        label: '上单中',
        value: 2,
    },
    {
        label: '审核通过待上线',
        value: 3,
    },
    {
        label: '上线',
        value: 1,
    },
    {
        label: '下线',
        value: 0,
    },
];

const ALL_COUNT_STATUS = ['-3', '-2', '-1', '0', '1', '2', '3', '4', '5'];// 全部线索status
const UNASSIGN_COUNT_STATUS = {// 待分配status
    STR_VALUE: ['-3', '-2', '-1', '0'],
    NUM_VALUE: [-3, -2, -1, 0],
};
const getUnAssignStatus = (bizId) => {// 待分配status
    if (bizId === BizEnum.QY) {
        return {// 待分配status
            STR_VALUE: ['1'],
            NUM_VALUE: [1],
        }
    } else {
        return {
            STR_VALUE: ['-3', '-2', '-1', '0'],
            NUM_VALUE: [-3, -2, -1, 0],
        }
    }
};

const AI_FOOLOW_STATUS = {
    ING: 1, //AI跟进中
    FINISH: 2, //AI已成功闭环
    FAIL: 3, //AI已失败闭环
    TO_BD: 4, //AI已转交人工
}

const AI_FOOLOW_STATUS_MAP = {
    1: 'AI跟进中',
    2: 'AI已成功闭环',
    3: 'AI已失败闭环',
    4: 'AI已转交人工',
}

const AI_FOOLOW_STATUS_LIST = [
    {
        label: 'AI跟进中',
        value: AI_FOOLOW_STATUS.ING,
    },
    {
        label: 'AI已成功闭环',
        value: AI_FOOLOW_STATUS.FINISH,
    },
    {
        label: 'AI已失败闭环',
        value: AI_FOOLOW_STATUS.FAIL,
    },
    {
        label: 'AI已转交人工',
        value: AI_FOOLOW_STATUS.TO_BD,
    },
]

export {
    WM_POI_ORDER_STATUS_LIST,
    WM_POI_ORDER_STATUS,
    ALL_COUNT_STATUS,
    UNASSIGN_COUNT_STATUS,
    getUnAssignStatus,
    AI_FOOLOW_STATUS,
    AI_FOOLOW_STATUS_MAP,
    AI_FOOLOW_STATUS_LIST,
};

