/* eslint-disable import/extensions */
/* eslint-disable import/no-unresolved */
import React from 'react';
import { ConfigProvider } from 'antd';
import Owl from '@dp/owl';
import zhCN from 'antd/lib/locale/zh_CN';
import '@roo/roo/theme/blue/index.css';
// import 'antd/dist/antd.css';
import { renderMicroApp } from '../../renderMicroApp';
import LeadList from './leadList';
import '../../assets/styles/index.scss'; // 加载全局css
import './leadList.scss';
// 接入 OWL
Owl.start({
    project: 'com.sankuai.wmmerchantfront.tmk.clue',
    pageUrl: 'leadListMine',
    devMode: process.env.ONLINE_ENV !== 'prod',
    resource: {
        sampleApi: 1,
    },
    page: {
        sample: 1,
    },
});
// const items = [
//     {
//         text: '电销新签工作台',
//         icon: 'home',
//     },
//     {
//         text: '我的线索',
//     },
// ];


const renderApp = () => {
    renderMicroApp(() => (<React.Fragment>
        <ConfigProvider locale={zhCN}>
            <div style={{ paddingLeft: 20 }}>
                {/* <Breadcrumb>
                    <Breadcrumb.Item key="电销新签工作台"><a href={bellwetherLinkParse('leadListMine.html')}>电销新签工作台</a></Breadcrumb.Item>
                    <Breadcrumb.Item key="我的线索" className="lead-detail-tx-blue">我的线索</Breadcrumb.Item>
                </Breadcrumb> */}

                <LeadList isMine />

            </div>
        </ConfigProvider>
    </React.Fragment>));
};

renderApp();
if (process.env.NODE_ENV === 'development') {
    if (module.hot) {
        module.hot.accept();
    }
}
