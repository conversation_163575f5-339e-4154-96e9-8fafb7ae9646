import { message } from 'antd';
import {
    useEffect,
    useState,
} from 'react';
import service from '../../../common/service';

const useFetchData = (url, params) => {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    useEffect(() => {
        setLoading(true);
        service.get(url, params)
            .then((res) => {
                if (res?.code !== 0) {
                    setLoading(false);
                    setError(res?.msg);
                    message.error(res?.msg);
                } else {
                    setLoading(false);
                    setData(res.data);
                }
            })
            .catch((err) => {
                setLoading(false);
                setError(err);
                // message.error(err && err.msg);
            });
    }, []);

    return [data, loading, error];
};

export default useFetchData;
