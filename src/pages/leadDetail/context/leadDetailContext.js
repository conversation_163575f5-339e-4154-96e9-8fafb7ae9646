import React, {
    useCallback,
    useContext,
} from 'react';


const LeadDetailContext = React.createContext(null);
export default LeadDetailContext;

export const useModal = () => {
    const { setVisible, leadDetail, refresh } = useContext(LeadDetailContext);

    const hide = useCallback(() => {
        setVisible(false);
    }, []);

    const show = useCallback(() => {
        let _leadDetail = leadDetail;
        if (!_leadDetail) {
            _leadDetail = window.__LeadDetail__;
        }
        setVisible(true);
    }, [leadDetail]);

    return [hide, show];
};
