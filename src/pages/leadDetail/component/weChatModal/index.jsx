/* eslint-disable no-unused-expressions */
/* eslint-disable eqeqeq */
/* eslint-disable no-console */
/* eslint-disable react/prop-types,import/extensions,import/no-unresolved, no-shadow */

import '@/assets/styles/leadDetail.scss';
import Wrapper from '@/components/wrapper';
import {
    Form,
    Input,
    message,
    Modal,
} from 'antd';
import React, {
    useContext,
    useEffect,
    useRef,
} from 'react';
import service from '../../../../common/service';
import { apiMap } from '../../../../common/service/apiMap';
import leadDetailContext from '../../context/leadDetailContext';

// 连续点击请求 限制
const hasReq = false;
const WeChatModal = ({
    visible,
    data,
    onCancel,
    onConfirm,
}) => {
    const {
        leadDetail,
    } = useContext(leadDetailContext);
    const [form] = Form.useForm();
    const phoneRef = useRef('');
    useEffect(() => {
        if (leadDetail && visible) {
            const wxPhoneInfo = data.qwPhoneList ? data.qwPhoneList[0] : null;
            if (wxPhoneInfo) {
                service.get(apiMap.decodePhone.path, {
                    key: wxPhoneInfo.qwPhone_control_view_key_info,
                    appKey: 'com.sankuai.waimaisales.newsigncenter',
                }).then((res) => {
                    if (res?.code === 0 && res?.data?.info) {
                        form.setFieldsValue({ WeChat: res?.data?.info });
                        phoneRef.current = res?.data?.info;
                    } else {
                        message.error(res?.msg);
                    }
                }).catch((err) => {
                    message.error(err && err.msg);
                });
            }
        }
    }, [leadDetail, visible]);


    const submitWeChat = async (weChat) => {
        if (!weChat) {
            message.error('请填写企业微信');
            return;
        }

        service.get(apiMap.saveQwInfo.path, {
            leadId: data?.leadId,
            phoneNumber: weChat,
        }).then((res) => {
            if (res?.code !== 0) {
                message.error(res?.msg || '查询不到微信信息');
                form.resetFields(['WeChat']);
            } else {
                message.success('绑定成功');
                onConfirm && onConfirm();
            }
        });
    };
    const judgeWeChatIsHave = async (value) => {
        if (!value) return;
        service.get(apiMap.queryQwInfo.path, {
            phoneNumber: value,
        }).then((res) => {
            if (res.code || res.code === 0) {
                submitWeChat(value);
            }
        }).catch(() => {
            message.error('查询不到该企业微信信息');
        });
    };


    return (
        <React.Fragment>
            <Modal
                closable={false}
                title="企微绑定"
                open={visible}
                onCancel={() => {
                    form.resetFields(['WeChat']);
                    if (onCancel) {
                        onCancel();
                    }
                }}
                okText="绑定"
                onOk={() => {
                    form.validateFields().then(() => {
                        judgeWeChatIsHave(form.getFieldValue('WeChat'));
                    });
                }}
            >
                <Form onFinish={submitWeChat} form={form} >
                    <div className="flex-row">
                        <Form.Item
                            className="ml-10"
                            label="企业微信"
                            name="WeChat"
                            style={{ width: 'calc(100% - 63.89px)' }}
                            rules={[{
                                required: true,
                                message: '请输入企业微信',
                            }]}
                        >
                            <Input
                                placeholder="请输入企业微信"
                                allowClear
                                onChange={(e) => {
                                    form.setFieldValue({ WeChat: e.target.value });
                                }}
                            />
                        </Form.Item>
                    </div>
                </Form>
            </Modal>
        </React.Fragment>
    );
};
export default WeChatModal;
