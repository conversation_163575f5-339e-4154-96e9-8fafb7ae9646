/* eslint-disable react/prop-types */

import AudioPlayer from '@mtfe/audio-player-next';
import React, {
    useCallback,
    useState,
} from 'react';
import { Modal } from 'antd';

import getPrefix from '../../../../common/service/getPrefix';
import ExtractTextsModal from './extractTextsModal';
import './index.scss';

const getEnv = () => {
    if (window.location.host === 'wm-ocrm.waimai.st.sankuai.com') {
        return 'st';
    } else if (window.location.host === 'wm-ocrm.waimai.test.sankuai.com' || window.location.host.indexOf('localhost') > -1) {
        return 'test';
    } else if (window.location.host === 'wm.ocrm.meituan.com') {
        return 'prod';
    } else {
        return 'prod';
    }
};
export default ({ src }) => {
    const [texts, setTexts] = useState([]);
    const [visible, setVisible] = useState(false);

    const openExtractTextModal = useCallback(() => {
        setVisible(true);
    }, []);

    const closeExtractTextModal = useCallback(() => {
        setVisible(false);
    }, []);

    const onExtractText = useCallback((textsData) => {
        setTexts(textsData);
        openExtractTextModal();
    }, []);

    return (
        <React.Fragment>
            <AudioPlayer
                src={src}
                extractText
                packageUrl={url => `${getPrefix(url)}${url}`}
                env={getEnv()}
                onExtractText={onExtractText}
                renderLoading={
                    ({ loading }) => (
                        <Modal
                            visible={loading}
                            footer={[]}
                            title={null}
                            closable={false}
                            showCancelButton={false}
                            showConfirmButton={false}
                        >
                            <div style={{ fontSize: 18, color: 'black' }}>
                                音频解析中, 请稍后...
                            </div>
                        </Modal>
                    )
                }
            />
            <ExtractTextsModal texts={texts} visible={visible} hideModal={closeExtractTextModal} />
        </React.Fragment>
    );
};
