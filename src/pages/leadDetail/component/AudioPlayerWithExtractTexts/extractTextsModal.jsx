/* eslint-disable react/prop-types */

import { Modal, Button } from 'antd';
import classNames from 'classnames';
import React from 'react';
import { parseTime } from '../../../../utils/utils';
import ClientAAvatar from './clientAAvatar';
import ClientBAvatar from './clientBAvatar';

import '@mtfe/audio-player-next/dist/index.css';

const MESSAGE_POSITION = {
    LEFT: 1,
    RIGHT: 0,
};
const USER_NAMES = [];
USER_NAMES[MESSAGE_POSITION.LEFT] = '讲话人A';
USER_NAMES[MESSAGE_POSITION.RIGHT] = '讲话人B';

export default ({
    visible,
    texts,
    hideModal,
}) => (
    <Modal
        title="对话内容"
        width={800}
        visible={visible}
        onCancel={() => {
            hideModal();
        }}
        showCancelButton={false}
        footer={[<Button
            type="primary"
            onClick={() => {
                hideModal();
            }}
        >关闭</Button>]}
    >
        <div className="etfa-container">
            {
                texts.map(line => (
                    <div
                        className={classNames(line.channel === MESSAGE_POSITION.RIGHT ? 'right' : 'left', 'basic')}
                    >
                        {
                            line.channel === MESSAGE_POSITION.RIGHT && <div className="empty" />
                        }
                        <div className="content">
                            <div className="avatar">
                                <div className="avatar-circle">
                                    {
                                        line.channel === MESSAGE_POSITION.LEFT ? <ClientAAvatar /> : <ClientBAvatar />
                                    }
                                </div>
                            </div>
                            <div className="info">
                                <div className="user-name">
                                    <span>{USER_NAMES[line.channel]}</span>
                                    <span>{`${parseTime(line.startTime)} - ${parseTime(line.endTime)}`}</span>
                                </div>
                                <div className="message">
                                    <span>{line.text}</span>
                                </div>
                            </div>
                        </div>
                        {
                            line.channel === MESSAGE_POSITION.LEFT && <div className="empty" />
                        }
                    </div>
                ))
            }
        </div>
    </Modal>
);
