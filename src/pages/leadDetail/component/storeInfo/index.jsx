/* eslint-disable import/extensions */
/* eslint-disable import/no-unresolved */

import React, { useMemo } from 'react';
import Info from '@/components/info';
import PropTypes from 'prop-types';
import Phones from './phones';
import WxShow from './wxShow';
import Editor from './editor';
import service from '../../../../common/service';
import { apiMap } from '../../../../common/service/apiMap';
import { Popover } from 'antd';

const storeInfoCols = [
    {
        label: '客户ID',
        prop: 'customerId',
    },
    {
        label: '客户名称',
        prop: 'customerName',
    },
    {
        label: '关联门店',
        prop: 'customerPois',
        render(row) {
            return row ? <Popover content={<div style={{ width: '600px', wordBreak: 'break-word', }}>{row.join(',')}</div>}><div className='ellipsis' style={{ maxWidth: 800, cursor: 'pointer' }}>{row.join(',')}</div></Popover> : '-';
        }
    },
    {
        label: '门店名称',
        prop: 'leadName',
    }, {
        label: '品类',
        prop: 'categoryName',
        defaultValue: '-',
    },

    {
        label: '物理城市',
        prop: 'cityName',
        defaultValue: '-',
    }, {
        label: '门店地址',
        prop: 'address',
        defaultValue: '-',
        render(row) {
            return <div style={{ maxWidth: 800, wordWrap: 'break-word' }}>{row}</div>
        }
    },
    {
        label: '联系人',
        prop: 'signer',
        defaultValue: '-',
    },
    {
        label: '联系电话',
        prop: 'phoneList',
        render: Phones,
        defaultValue: '-',
    },
    {
        label: '商家微信号',
        prop: 'qwPhoneList',
        render: row => row ? <WxShow qwPhoneList={row} /> : '-',
        defaultValue: '-',
    },
    {
        label: 'KP姓名',
        prop: 'kpInfoDTOS',
        render: row => row && row[0].name,
        showLogic: row => !!(row && row[0].name),

    },
    {
        label: '企业规模',
        prop: 'enterpriseScale',
        showLogic: text => !!text,
    },
    {
        label: '预期开店城市',
        prop: 'hopeCity',
        showLogic: text => !!text,
    },
    {
        label: '合作需求',
        prop: 'cooperationDemand',
        showLogic: text => !!text,
    },
];

// 动态字段展示
function getDynamicField(data) {
    const { customerInfos } = data;
    if (customerInfos && customerInfos.length) {
        return customerInfos.map(item => ({
            label: item.desc,
            prop: item.code,
            defaultValue: item.value,
        }));
    } else {
        return [];
    }
}

const StoreInfo = ({ data, auth }) => {
    const { phoneList } = data;

    useMemo(() => {
        if (phoneList) {
            const phonekey = 'phoneNumber_control_view_key_info';
            phoneList.map((val) => {
                const url = apiMap.decodePhone.path;
                const params = {
                    key: val[phonekey],
                    appKey: 'com.sankuai.waimaisales.newsigncenter',
                };
                service.get(url, params).then((res) => {
                    if (res.code === 0) {
                        val.info = res.data.info;
                    }
                }).catch(() => {
                });
                return val;
            });
        }
    }, [phoneList]);
    return (
        <Info
            title={<Editor data={data} auth={auth} />}
            data={data}
            cols={storeInfoCols.concat(getDynamicField(data))}
        />
    );
};

StoreInfo.propTypes = {
    data: PropTypes.object,
    auth: PropTypes.object,
};
StoreInfo.defaultProps = {
    data: {},
    auth: {},
};

export default StoreInfo;
