/* eslint-disable react/prop-types,import/extensions,import/no-unresolved, camelcase */

import { QuestionCircleOutlined } from '@ant-design/icons';
import {
    Button,
    message,
    Modal,
    Tooltip,
} from 'antd';
import React, {
    useCallback,
    useContext,
    useState,
} from 'react';
import { getUrlParam } from '@wmfe/util_param';
import useOutBound, { CallerType, CalleeType } from '@mfe/cc-outbound';

import service from '../../../../common/service';
import { apiMap } from '../../../../common/service/apiMap';
import PrimaryButton from '../../../../components/primaryButton';

import Wrapper from '../../../../components/wrapper';
import { phoneEncrypt } from '../../../../utils/utils';
import { BizEnum } from '../../../../utils/const';
import leadDetailContext, { useModal } from '../../context/leadDetailContext';

const bizId = getUrlParam('bizId') || '';

const phoneNumber = 'phoneNumber';

const PhonesModal = ({
    phones,
    visible,
    closeModal,
}) => {
    const {
        leadId,
        poiId,
        callBtnDisabled,
        setDoveCallId,
        refresh,
        leadDetail,
    } = useContext(leadDetailContext);
    const [, openMarkModal] = useModal();

    // 初始化
    const { callPhoneNumber } = useOutBound({
        calleeType: CalleeType.WM_POI,
        callerType: BizEnum.QY === +bizId ? 7 : CallerType.TELEMARKETING, // 企业发票业务 外呼daniClassifyId新加参数 7
        preCallPrefix: process.env.VITE_API_PREFIX ? '/xianfu/api/clue/dovecallapi' : '',
        hideAfterCall: true,
        onEventError: (arg) => {
            console.log(arg);
            if (arg?.ErrorCode === 60003) {
                return;
            }
            message.error(arg.ErrorMessage || arg.message || '外呼异常');
        },
        onEventEstablish: (d) => {
            console.log(`通话建立: ${d.ContactID}`, d);
        },
        onEventReleased: (d) => {
            console.log(`通话结束: ${d.ContactID}`, d);
            openMarkModal();
            setDoveCallId(d?.UserData?.appCallUuid || d?.appCallUuid);
        },
        onPreCallRespond: (data) => {
            service.post(apiMap.call.path, {
                leadId,
                doveCallId: data?.appCallUuid,
            }).then((res2) => {
                if (res2?.code === 0) {
                    refresh();
                } else {
                    message.error(res2.msg || '通话记录保存失败');
                }
            }).catch(err => message.error((err && err.msg) || '通话记录保存失败'));
        },
        mxConfig: {
            delAnswerBtn: true,
        },
    });

    const phoneNumber_control_view_key_info = 'phoneNumber_control_view_key_info';
    const phoneNumber_button_control = 'phoneNumber_button_control';
    const callPhone = useCallback(async (phone) => {
        service.get(apiMap.decodePhone.path, {
            key: phone[phoneNumber_control_view_key_info],
            appKey: 'com.sankuai.waimaisales.newsigncenter',
        }).then((res) => {
            if (res?.code === 0) {
                closeModal();
                let standardPhone = res?.data?.info;
                standardPhone = standardPhone.replace(/-/, '');
                callPhoneNumber(standardPhone, [BizEnum.QY, BizEnum.WM].includes(+bizId) ? poiId : leadId);
            } else {
                message.error(res?.msg);
            }
        }).catch((err) => {
            message.error(err && err.msg);
        });
    }, [poiId]);

    return (
        <Modal
            open={visible}
            onCancel={closeModal}
            title="查看联系电话"
            closable={false}
            width={400}
            footer={[<Button onClick={closeModal}>关闭</Button>]}
        >
            <Wrapper flex flexColumn alignCenter>
                <Wrapper flex justifyCenter noPadding>
                    <div className="lead-detail-mt-5px lead-detail-mr-sm">
                        {'电话号码: '}
                    </div>
                    <dl style={{ margin: 0 }}>
                        {
                            phones.map(phone =>
                            (
                                <dd
                                    key={phone[phoneNumber]}
                                    className="lead-detail-flex lead-detail-align-center"
                                >
                                    <span style={{ flex: 1 }}>{`${BizEnum.SG}` === bizId ? phoneEncrypt(phone.info) : phone.info}</span>
                                    {
                                        phone[phoneNumber_button_control] &&
                                        <PrimaryButton
                                            onClick={() => callPhone(phone)}
                                            disabled={BizEnum.QY !== +bizId ? callBtnDisabled : [1, 0].includes(leadDetail?.status)}
                                            className="lead-detail-mr-sm"
                                        >
                                            外呼
                                        </PrimaryButton>
                                    }
                                    {
                                        (BizEnum.QY !== +bizId ? callBtnDisabled : false) &&
                                        <Tooltip title="该线索已闭环,无法外呼">
                                            <QuestionCircleOutlined style={{
                                                fontSize: 20,
                                                color: 'gray',
                                            }}
                                            />
                                        </Tooltip>
                                    }
                                </dd>))
                        }
                    </dl>
                </Wrapper>

            </Wrapper>
        </Modal>
    );
};

// phones: [
// {
// phoneNumber: string
// phoneNumber_control_view_key_info: string
// phoneNumber_button_control: 0 || 1
// }
// ]
const Phones = (phones = []) => {
    const [visible, setVisible] = useState(false);
    const { permissions } = useContext(leadDetailContext);

    const closeModal = useCallback(() => setVisible(false), []);
    const openModal = useCallback(() => {
        setVisible(true);
    }, []);
    return (
        phones && phones.length > 0 ?
            <React.Fragment>
                <dl className="no-margin">
                    {phones.map(phone => <dd key={phone[phoneNumber]}>{phone[phoneNumber]}</dd>)}
                </dl>
                {permissions?.phone_see &&
                    <PrimaryButton onClick={openModal}>
                        查看
                    </PrimaryButton>
                }
                <PhonesModal phones={phones} closeModal={closeModal} visible={visible} />

            </React.Fragment> : '-'
    );
};
export default Phones;
