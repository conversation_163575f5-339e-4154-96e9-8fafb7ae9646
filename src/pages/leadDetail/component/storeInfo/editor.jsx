import React, { useState, createRef, useContext } from 'react';
import { Modal, message, Button, Input, Select } from 'antd';
import { DifferentItem } from '@mfe/diff-react';
import { getUrlParam } from '@wmfe/util_param';
import PropTypes from 'prop-types';
import ClueForm from './clueForm';
import service from '../../../../common/service';
import { apiMap } from '../../../../common/service/apiMap';
// import LeadDetailContext, { useModal } from './context/leadDetailContext';
import LeadDetailContext from '../../context/leadDetailContext';
import { BizEnum } from '../../../../utils/const';
import ClueFormForQY from './clueFormForQY';


const activeBizId = getUrlParam('bizId', document.location.href);

export default function editor({ data, auth }) {
    const [state, setState] = useState({
        visible: false,
        realPhone: [],
    });
    const {
        address, categoryId, categoryName, phoneList = [], cityName, leadName, customerInfos, channelSource, auxFollowerName, auxFollowerMisId, auxFollowerUid, qwPhoneList
    } = data;
    const cluesRef = createRef();
    const { leadId, refresh } = useContext(LeadDetailContext);

    // 提交
    const submitValue = () => {
        cluesRef.current.tj((tjData) => {
            const { contactPhone, contactPhone2, categoryData, weChatPhone, auxFollowerUid } = tjData;
            // 品类 城市 为下拉框，做值未改变处理
            const category = tjData.categoryId === categoryName ? categoryId : categoryData;
            // 联系电话组装为数组
            const contentPhones = [];
            if (contactPhone) {
                contentPhones.push(contactPhone);
            }
            if (contactPhone2) {
                contentPhones.push(contactPhone2);
            }

            const request = {
                // cityId: city, // 城市id
                leadId: Number(leadId), // 线索id
                leadName: tjData.leadName, // 门店名称
                categoryId: category, // 三级品类ID
                address: tjData.address, // 门店地址
                contentPhones,
                bizId: activeBizId,
                weChatPhone,
                auxFollowerUid,
            };

            // // 全景渠道没有 电话
            // if (channelSource !== 26) {
            //     request.contentPhones = contentPhones;// 联系电话
            // }

            //  组装自定义字段
            if (customerInfos && customerInfos.length > 0) {
                const newCustomerInfos = {};
                customerInfos.forEach((item) => {
                    newCustomerInfos[item.code] = tjData[item.code] || '';
                    if (item.code === 'hopeCity' && Array.isArray(tjData[item.code])) {
                        newCustomerInfos[item.code] = newCustomerInfos[item.code].pop();
                    }
                });

                request.customerInfos = newCustomerInfos;
            }
            service.post(apiMap.updataClues.path, { ...request }).then((res) => {
                if (res.code === 0) {
                    refresh(); // 刷新页面接口
                    setTimeout(() => {
                        message.success('编辑成功');
                    }, 500);
                }
            }).catch(() => {
                setTimeout(() => {
                    message.error('编辑失败');
                }, 500);
            }).finally(() => {
                // 关闭弹框
                setState({
                    ...state,
                    visible: false,
                });
            });
        });
    };

    // 取消
    const cancel = () => {
        // 清空表单
        cluesRef.current.resetFields();
        // 关闭弹框
        setState({
            ...state,
            visible: false,
        });
    };

    // 编辑弹框
    const editorValue = () => {
        // 打开弹框
        setState({
            ...state,
            visible: true,
        });
    };
    return (
        <div className="editor-div-names">
            <DifferentItem diffId='title' >
                {({ data }) => {
                    const text = data.Text;
                    return <span>{text || '门店信息'}</span>
                }}
            </DifferentItem>
            {
                data && !!data.claimMisId && auth && auth.uiControl && auth.uiControl.newsign_create
                    ?
                    <Button onClick={editorValue}>编辑</Button>
                    : null
            }

            <Modal
                title="编辑信息"
                visible={state.visible}
                onOk={submitValue}
                onCancel={cancel}
                okText="保存"
                width={800}
            >
                {
                    BizEnum.QY === +activeBizId ? <ClueFormForQY
                        ref={cluesRef}
                        data={{
                            auxFollowerUid,
                            auxFollowerMisId,
                            auxFollowerName,
                            qwPhoneList: qwPhoneList
                        }}></ClueFormForQY> :
                        <ClueForm
                            ref={cluesRef}
                            data={{
                                categoryId: categoryName, // 三级品类ID
                                cityId: cityName, // 二级物理城市ID
                                contactPhone: phoneList[0]?.info, // 联系电话1
                                contactPhone2: phoneList[1]?.info, // 联系电话2
                                address, // 门店地址
                                leadName, // 门店名称
                                channelSource,
                                activeBizId
                            }}
                            customerInfos={customerInfos}
                        />
                }


            </Modal >
        </div >
    );
}

editor.propTypes = {
    data: PropTypes.object,
};
editor.defaultProps = {
    data: {},
};
