/* eslint-disable no-unused-expressions */
import React, { useEffect, forwardRef, useImperativeHandle, useContext } from 'react';
import { Form, Input, Row, Col } from 'antd';
import PropTypes from 'prop-types';
import SelectUser from '../../../../components/selectUser';
import service from '../../../../common/service';

const { Item } = Form;

function CluesForm(props) {
    const [form] = Form.useForm();
    const { auxFollowerUid, auxFollowerName, auxFollowerMisId, qwPhoneList } = props?.data || {};

    // 操作表单
    const resetFields = (arr) => {
        if (arr && arr.length) {
            form.resetFields(arr);
        } else {
            form.resetFields(); // 清空全部
        }
    };

    useEffect(() => {
        if (qwPhoneList && qwPhoneList.length) {
            const weChatPhone = qwPhoneList.pop();
            service.get(service.apiMap.decodePhone.path, {
                key: weChatPhone.qwPhone_control_view_key_info,
                appKey: 'com.sankuai.waimaisales.newsigncenter',
            }).then((res) => {
                if (res?.code === 0) {
                    const standardPhone = res?.data?.info;
                    setTimeout(() => {
                        form.setFieldsValue({ auxFollowerUid, weChatPhone: standardPhone });
                    }, 1);
                }
            })
        }

    }, [props.data]);

    useImperativeHandle(props.refInstance, () => ({
        tj: submit,
        resetFields,
    }));

    const submit = (callback, callback2, callback3) => {
        form.validateFields().then((values) => {
            callback(values);
        }).catch((err) => {
            if (callback2) { callback2(err) }
        }).finally((fin) => {
            if (callback3) { callback3(fin) }
        });
    };

    return (
        <div style={{ overflow: 'auto', maxHeight: 600 }}>
            <Form
                form={form}
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
                autoComplete="off"
                initialValues={{ ...props.data }}
            >
                <Row>
                    <Col span={21}>
                        <Item
                            name="auxFollowerUid"
                            label="辅助跟进人"
                        >
                            <SelectUser placeholder="请输入辅助跟进人" bindValue={auxFollowerUid ? { name: auxFollowerName, mis: auxFollowerMisId, uid: auxFollowerUid } : null} />
                        </Item>
                    </Col>
                    <Col span={21}>
                        <Item
                            name="weChatPhone"
                            label="商家微信号"
                            rules={[
                                { max: 100, message: '最多100个字' },
                            ]}
                        >
                            <Input placeholder="请输商家微信号" maxLength={100} />
                        </Item>
                    </Col>


                </Row>
            </Form>
        </div>

    );
}

CluesForm.propTypes = {
    refInstance: PropTypes.object.isRequired,
    data: PropTypes.object,
};
CluesForm.defaultProps = {
    data: {},
};

export default forwardRef((props, ref) => <CluesForm {...props} refInstance={ref} />);
