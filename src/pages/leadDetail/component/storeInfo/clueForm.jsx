/* eslint-disable no-unused-expressions */
import React, { useState, useEffect, forwardRef, useImperativeHandle, useContext } from 'react';
import { Select, Cascader, Form, Input, Row, Col } from 'antd';
import { PlusCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
import useCates from '../../../../hooks/useCates';
import LeadDetailContext from '../../context/leadDetailContext';
import useCitysTopLevel from '../../../../hooks/useCitysTopLevel';
import SelectUser from '../../../../components/selectUser';
import { BizEnum } from '../../../../utils/const';

const { Option } = Select;
const { Item } = Form;

function CluesForm(props) {
    const [form] = Form.useForm();
    const cates = useCates(false);
    const { optionInfo } = useContext(LeadDetailContext);
    const cityInfo = useCitysTopLevel();
    const [state, setState] = useState({
        addContactPhone: !!props.data?.contactPhone2, // 编辑页，如果存在第二个联系电话，则默认为true
        categoryData: null,
    });

    // 联系电话添加
    const contactPhoneAdd = () => {
        setState({
            ...state,
            addContactPhone: true,
        });
    };

    // 联系电话减少
    const contactPhoneReduction = () => {
        setState({
            ...state,
            addContactPhone: false,
        });
    };

    // 提交表单
    const tj = (callback, callback2, callback3) => {
        form.validateFields().then((values) => {
            values.categoryData = state.categoryData;
            callback(values);
        }).catch((err) => {
            if (callback2) { callback2(err) }
        }).finally((fin) => {
            if (callback3) { callback3(fin) }
        });
    };

    // 操作表单
    const resetFields = (arr) => {
        if (arr && arr.length) {
            form.resetFields(arr);
        } else {
            form.resetFields(); // 清空全部
        }
    };


    useEffect(() => {
        const customerData = {};
        props.customerInfos && props.customerInfos.forEach((item) => {
            customerData[item.code] = item.value;
        });

        setTimeout(() => {
            form.setFieldsValue({ ...props.data, ...customerData });
        }, 1);
    }, [props.customerInfos, props.data]);

    useImperativeHandle(props.refInstance, () => ({
        tj,
        resetFields,
    }));

    return (
        <div style={{ overflow: 'auto', maxHeight: 600 }}>
            <Form
                form={form}
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
                autoComplete="off"
                initialValues={{ ...props.data }}
            >
                <Row>
                    <Col span={21}>
                        <Item
                            name="leadName"
                            label="门店名称"
                            rules={[
                                { required: true, message: '门店名称不得为空' },
                                { max: 15, message: '最多15个字' },
                            ]}
                        >
                            <Input placeholder="请输入门店名称" maxLength={30} />
                        </Item>
                    </Col>
                    <Col span={21}>
                        <Item
                            name="categoryId"
                            label="品类"
                            rules={[{ required: true, message: '品类不得为空' }]}
                        >
                            <Cascader
                                style={{ width: '100%' }}
                                showSearch={{
                                    filter: (inputValue, path) => path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1),
                                }}
                                options={cates}
                                onChange={(val) => {
                                    let da = null;
                                    if (val) {
                                        const _val = [...val];
                                        const pop = _val.pop();// 移除最后一个数组元素
                                        if (pop === -1) {
                                            da = _val.length === 0 ? null : _val.pop();
                                        } else {
                                            da = pop;
                                        }
                                    }
                                    setState({
                                        ...state,
                                        categoryData: da,
                                    });
                                }}
                                placeholder="请选择品类"
                            />
                        </Item>
                    </Col>
                    <Col span={21}>
                        <Item
                            name="address"
                            label="门店地址"
                            rules={[
                                { required: true, message: '门店地址不得为空' },
                                { max: 40, message: '最多40个字' },
                            ]}
                        >
                            <Input placeholder="请输入门店地址" maxLength={70} />
                        </Item>
                    </Col>
                </Row>
                {
                    // 渠道 全景没有 电话号码
                    <Row>
                        <Col span={21}>
                            <Item
                                name="contactPhone"
                                label="联系电话"
                                rules={
                                    (props.data.channelSource !== 26 ? [{ required: true, message: '联系电话不得为空' }] : []).concat([
                                        { pattern: /^[\d-]{11,12}$|^\d{7,8}$|^1{1}\d{10}$/, message: '电话号码位数有误' },
                                    ])}
                            >
                                <Input placeholder="请输入联系电话" maxLength={20} />
                            </Item>
                        </Col>
                        <Col span={3}>
                            <div className="contactPhone-div-add" >
                                {
                                    !state.addContactPhone
                                        ? <PlusCircleOutlined // 加号
                                            style={{ fontSize: 18 }}
                                            onClick={contactPhoneAdd}
                                        />
                                        : null
                                }
                            </div>
                        </Col>
                    </Row>
                }

                {
                    state.addContactPhone
                        ? <Row>
                            <Col span={21}>
                                <Item
                                    className="contactPhone-name2"
                                    colon={false}
                                    name="contactPhone2"
                                    label=" "
                                    rules={(props.data.channelSource !== 26 ? [{ required: true, message: '联系电话不得为空' }] : []).concat([
                                        { pattern: /^[\d-]{11,12}$|^\d{7,8}$|^1{1}\d{10}$/, message: '电话号码位数有误' },
                                    ])}
                                >
                                    <Input placeholder="请输入联系电话" maxLength={20} />
                                </Item>
                            </Col>
                            <Col span={3}>
                                <div className="contactPhone-div-add" >
                                    <MinusCircleOutlined // 减号
                                        style={{ fontSize: 18 }}
                                        onClick={contactPhoneReduction}
                                    />
                                </div>
                            </Col>
                        </Row>
                        : null
                }

                {
                    props.customerInfos && props.customerInfos.map((item) => {
                        const { requiredField, code, desc } = item;
                        return (<Row>
                            <Col span={21}>

                                {
                                    item.formType === 'textInput' ? <Item
                                        name={code}
                                        label={desc}
                                        rules={[
                                            requiredField ? { required: true, message: `${desc}不得为空` } : null,
                                        ]}
                                    >
                                        <Input placeholder={`请输入${desc}`} />
                                    </Item> : null
                                }
                                {
                                    item.formType === 'select' ? <Item
                                        name={code}
                                        label={desc}
                                        rules={[
                                            requiredField ? { required: true, message: `${desc}不得为空` } : null,
                                        ]}
                                    >
                                        <Select
                                            placeholder={`请选择${desc}`}
                                            fieldNames={{
                                                label: 'name',
                                                value: 'name',
                                            }}
                                            options={optionInfo[code] || []}
                                        />
                                    </Item> : null
                                }
                                {
                                    item.formType === 'citySelector' ?
                                        <Item
                                            name={code}
                                            label={desc}
                                            rules={[
                                                requiredField ? { required: true, message: `${desc}不得为空` } : null,
                                            ]}
                                        >
                                            <Cascader
                                                placeholder={`请选择${desc}`}
                                                showSearch={(inputValue, path) =>
                                                    path.some(option => (option.label).toLowerCase().indexOf(inputValue.toLowerCase()) > -1)}
                                                fieldNames={{
                                                    label: 'name',
                                                    value: 'name',
                                                    children: 'children',
                                                }}
                                                options={cityInfo || []}
                                            />
                                        </Item> : null
                                }
                            </Col>
                        </Row>
                        );
                    })
                }
            </Form>
        </div>

    );
}

CluesForm.propTypes = {
    refInstance: PropTypes.object.isRequired,
    data: PropTypes.object,
    customerInfos: PropTypes.array,
};
CluesForm.defaultProps = {
    data: {},
    customerInfos: [],
};

export default forwardRef((props, ref) => <CluesForm {...props} refInstance={ref} />);
