/* eslint-disable react/prop-types */
import React, { useContext } from 'react';
import { Button, message, Modal, Row, Col } from 'antd';
import leadDetailContext from '../../context/leadDetailContext';
import service from '../../../../common/service';
import { apiMap } from '../../../../common/service/apiMap';


const WxShow = (props) => {
    const { qwPhoneList } = props;
    const [modal, contextHolder] = Modal.useModal();
    const { permissions } = useContext(leadDetailContext);

    return (<div>
        {
            qwPhoneList && qwPhoneList.length > 0 ?
                qwPhoneList.map(item => (
                    permissions?.phone_see ? <div>
                        <span>{item.qwPhone}</span>
                        <Button
                            type="primary"
                            style={{ marginLeft: 10, borderRadius: 4 }}
                            onClick={() => {
                                service.get(apiMap.decodePhone.path, {
                                    key: item.qwPhone_control_view_key_info,
                                    appKey: 'com.sankuai.waimaisales.newsigncenter',
                                }).then((res) => {
                                    if (res?.code === 0) {
                                        const standardPhone = res?.data?.info;
                                        modal.info({
                                            icon: null,
                                            title: '查看商家微信信息',
                                            content: <Row style={{ marginTop: 10 }}><Col>商家微信号：</Col><Col>{standardPhone || '-'}</Col></Row>,
                                        });
                                    } else {
                                        message.error(res?.msg);
                                    }
                                }).catch((err) => {
                                    message.error(err && err.msg);
                                });
                            }}
                        >
                            查看
                        </Button></div> : '-'))

                : '-'
        }
        {
            contextHolder
        }
    </div>);
};
export default WxShow;
