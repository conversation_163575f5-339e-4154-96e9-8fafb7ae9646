/* eslint-disable eqeqeq */
/* eslint-disable no-console */
/* eslint-disable react/prop-types,import/extensions,import/no-unresolved, no-shadow */

import '@/assets/styles/leadDetail.scss';
import Wrapper from '@/components/wrapper';
import {
    Button,
    Form,
    Input,
    message,
    Modal,
    Select,
} from 'antd';
import React, {
    useContext,
    useEffect,
    useMemo,
    useRef,
    useState,
} from 'react';
import service from '../../../../common/service';
import { apiMap } from '../../../../common/service/apiMap';
import PrimaryButton from '../../../../components/primaryButton';
import leadDetailContext from '../../context/leadDetailContext';
import { VisitType } from '../../../../utils/const';
import { DifferentItem } from '@mfe/diff-react';

const rules = [
    {
        required: true,
        message: '请选择',
    },
];

const plat = (labels) => {
    let arr = [];
    if (Array.isArray(labels)) {
        labels.forEach((item) => {
            arr.push(item);
            if (item.childLabel) {
                item.childLabel.forEach((per) => {
                    per.parentId = item.labelId;
                });
                const children = plat(item.childLabel);

                arr = arr.concat(children);
            }
        });
    }
    return arr;
};
const option2antd = options => options && options.map(option => (
    {
        value: option.labelId,
        label: option.comment,
    }));
// 连续点击请求 限制
let hasReq = false;
const MarkModal = ({
    visible,
    hideModal,
    data,
    bizId,
}) => {
    const {
        leadId,
        doveCallId,
        leadDetail,
        refresh,
        setDoveCallId,
    } = useContext(leadDetailContext);
    const [form] = Form.useForm();
    const [options, setOptions] = useState(null);
    const optionsFlat = useRef([]);

    useEffect(() => {
        if (leadDetail) {
            form.setFieldsValue({ WeChat: data.qwInfoDTOS ? data.qwInfoDTOS[0]?.phoneNumber : '' });
            service.get(apiMap.options.path, {
                processCode: leadDetail?.status,
                bizId,
            })
                .then((res) => {
                    if (res?.code !== 0) {
                        message.error(res?.msg);
                    } else {
                        setOptions(res?.data);
                        optionsFlat.current = plat(res?.data);
                    }
                })
                .catch(err => message.error(err && err.msg));
        }
    }, [leadDetail]);

    const [firstLevel, setFirstLevel] = useState(null);
    const [secondLevel, setSecondLevel] = useState(null);
    const [thirdLevel, setThirdLevel] = useState(null);
    const [remark, setRemark] = useState('');
    const [WeChat, setWeChat] = useState('');
    const phoneNumber = useMemo(() => {
        if (data && data.qwInfoDTOS) {
            return data.qwInfoDTOS[0]?.phoneNumber;
        } else {
            return '';
        }
    }, [data]);


    const options2 = useMemo(() => {
        const options = optionsFlat.current.filter(item => item.parentId === firstLevel);
        if (leadDetail && leadDetail.channelSource === 2 && leadDetail.status === 3) {
            const index = options.findIndex(item => item.leadId === 12);
            if (index > -1) {
                options.splice(index, 1);
            }
        }
        return options;
    }, [firstLevel]);

    const options3 = useMemo(
        () => optionsFlat.current.filter(item => item.parentId === secondLevel && item.parentId !== undefined),
        [firstLevel, secondLevel],
    );


    const formItems = options && (
        [
            {
                label: '跟进结果',
                name: 'firstLevel',
                key: 'firstLevel',
                rules,

                options: option2antd(options),
                placeholder: '请选择',
                value: firstLevel,
                onChange: (value) => {
                    setFirstLevel(value);
                    setSecondLevel(undefined);
                    setThirdLevel(undefined);
                    if (form) {
                        form.resetFields(['secondLevel', 'thirdLevel']);
                    }
                },
            },
            {
                label: '拜访方式',
                name: 'visitType',
                key: 'visitType',
                rules: [{
                    required: true,
                    message: '请选择',
                }],
                options: VisitType,
                placeholder: '请选择',
            },
            {
                label: '标签类型',
                name: 'secondLevel',
                key: 'secondLevel',
                rules,
                options: option2antd(options2),
                placeholder: '请选择',
                value: secondLevel,
                onChange: (value) => {
                    setSecondLevel(value);
                    setThirdLevel(undefined);
                    if (form) {
                        form.resetFields(['thirdLevel']);
                    }
                },
            },
            {
                label: '具体内容',
                name: 'thirdLevel',
                key: 'thirdLevel',
                rules,

                options: option2antd(options3),
                placeholder: '请选择',
                value: thirdLevel,
                onChange: (value) => {
                    setThirdLevel(value);
                },
            },
        ]
    );

    const onConfirm = async () => {
        if (hasReq) {
            return;
        }
        hasReq = true;
        service.get(apiMap.alterLabel.path, {
            leadId,
            labelId: thirdLevel,
            processCode: leadDetail.status,
            doveCallId,
            visitType: form.getFieldValue('visitType'),
            remark,
        })
            .then((res) => {
                hasReq = false;
                if (res.code === 0) {
                    message.success('保存成功');
                    hideModal();
                    setFirstLevel(null);
                    setSecondLevel(null);
                    form.resetFields(['firstLevel', 'secondLevel', 'thirdLevel', 'remark']);
                } else {
                    message.error(res.msg || '保存失败');
                }
                refresh();
            })
            .catch(() => {
                hasReq = false;
                message.error('保存失败');
            });
        setDoveCallId(null);
    };

    const onCancel = () => {
        hideModal();
        form.resetFields(['firstLevel', 'secondLevel', 'thirdLevel', 'visitType', 'WeChat', 'remark']);
        setFirstLevel(null);
        setSecondLevel(null);
    };

    const _renderSelect = () => formItems.map(({
        label,
        name,
        rules,
        key,
        options,
        placeholder,
        onChange,
        value,
    }) => (
        <DifferentItem diffId={name}>
            {
                ({ data }) => {
                    const text = data.Text;
                    return <Form.Item
                        label={text || label}
                        name={name}
                        rules={rules}
                        key={key}
                        initialValue={undefined}
                    >
                        <Select
                            options={options}
                            placeholder={placeholder}
                            onChange={onChange}
                            value={value}
                        />
                    </Form.Item>
                }

            }
        </DifferentItem>
    ));

    const _renderFooter = () => (
        <Form.Item>
            <Wrapper flex justifyCenter>
                <PrimaryButton htmlType="submit" key="confirm">
                    确认
                </PrimaryButton>
                <Button
                    className="lead-detail-ml-sm lead-detail-gray"
                    onClick={onCancel}
                    key="cancel"
                >
                    取消
                </Button>
            </Wrapper>
        </Form.Item>);

    return (
        <React.Fragment>
            {!!options && <Modal closable={false} title="线索打标" visible={visible} footer={null}>

                <Form onFinish={onConfirm} form={form} >
                    {
                        _renderSelect()
                    }
                    <Form.Item
                        label="备注"
                        name="remark"
                        key="remark"
                        initialValue=""
                        labelCol={{
                            span: 4,
                        }}
                        rules={[
                            { max: 20, message: '最多20个字' },
                        ]}
                    >
                        <Input placeholder="请输入备注" onChange={e => setRemark(e.target.value)} />
                    </Form.Item>

                    {
                        _renderFooter()
                    }

                </Form>
            </Modal>}
        </React.Fragment >
    );
};
export default MarkModal;

