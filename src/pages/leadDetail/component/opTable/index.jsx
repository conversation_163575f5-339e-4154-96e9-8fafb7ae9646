/* eslint-disable react/prop-types */

import { useAntdTable } from 'ahooks';
import { Table } from 'antd';
import React, { useContext } from 'react';
import service from '../../../../common/service';
import { timeRender } from '../../../../utils/utils';
import LeadDetailContext from '../../context/leadDetailContext';

const { apiMap } = service;

const defaultValue = data => data || '-';

const opCols = [
    {
        title: '序号',
        dataIndex: 'opUid',
        width: 100,
        render: (text, record, index) => index + 1,
    },
    {
        title: '操作人',
        dataIndex: 'operatorDesc',
        width: 200,
        render: defaultValue,

    }, {
        title: '操作说明',
        dataIndex: 'opDesc',

        render: (text) => {
            if (text) {
                const arr = text.split('$_$_$');
                return arr.map(item => <div style={{ marginTop: 10 }}>{item}</div>);
            } else {
                return '-';
            }
        },

    }, {
        title: '操作时间',
        dataIndex: 'opTime',

        render: time => (
            time ? timeRender(time) : '-'),
    }, {
        title: '线索状态',
        dataIndex: 'statusDesc',
        width: 150,
        render: defaultValue,
    },
];

const fetchTableData = leadId => ({
    current,
    pageSize,
}) =>
    service.get(apiMap.opData.path, {
        pageNum: current,
        pageSize,
        leadId,
    })
        .then(res => res.data)
        .then(data => (
            {
                total: data.total,
                list: data.logs.sort((a, b) => b.opTime - a.opTime),
            }));

const OpTable = () => {
    const {
        leadId,
        leadDetail,
    } = useContext(LeadDetailContext);
    const { tableProps } = useAntdTable(fetchTableData(leadId), {
        refreshDeps: [leadDetail],
    });
    return <Table columns={opCols} rowKey={record => JSON.stringify(record)} {...tableProps} />;
};

export default OpTable;
