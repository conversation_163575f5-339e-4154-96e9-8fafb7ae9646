/* eslint-disable react/prop-types */

import '@mtfe/audio-player-next/dist/index.css';
import { useAntdTable } from 'ahooks';

import {
    message,
    Table as TableA,
} from 'antd';
import React, { useContext } from 'react';
import service from '../../../../common/service';
import { timeRender } from '../../../../utils/utils';
import LeadDetailContext from '../../context/leadDetailContext';
import AudioPlayerWithExtractTexts from '../AudioPlayerWithExtractTexts';
import { TableAgency } from '@mfe/diff-react';
const Table = TableAgency(TableA);
const { apiMap } = service;

const defaultValue = data => data || '-';

const markCols = [
    {
        title: '操作人',
        dataIndex: 'operatorDesc',
        render: defaultValue,
    },
    {
        title: '线索状态',
        dataIndex: 'statusDesc',
        render: defaultValue,
    },
    {
        title: '通话记录',
        dataIndex: 'callRecordUrl',
        render: (_, record) => {

            const { callRecord } = record;
            return callRecord
                ? callRecord.callRecordUrl
                    ? <AudioPlayerWithExtractTexts src={callRecord.callRecordUrl} />
                    : record.phoneCallDesc
                : '-'
        }
    },
    {
        title: '外呼开始时间',
        dataIndex: 'callRecordStartTime',
        render: (_, record) => {
            const { callRecord } = record;
            return callRecord ? callRecord.startTime : '-'
        }
    }, {
        title: '外呼结束时间',
        dataIndex: 'callRecordEndTime',
        render: (_, record) => {
            const { callRecord } = record;
            return callRecord ? callRecord.endTime : '-'
        }
    }, {
        title: '跟进结果',
        dataIndex: 'labelProcess',
        render: defaultValue,
    },
    {
        title: '拜访方式',
        dataIndex: 'visitType',
        render: defaultValue,
    }, {
        title: '标签类型',
        dataIndex: 'labelType',
        render: defaultValue,
    }, {
        title: '具体内容',
        dataIndex: 'labelContent',
        render: defaultValue,
    }, {
        title: '记录时间',
        dataIndex: 'recordTime',
        render: time => (
            time ? timeRender(time) : '-'),
    },
];

const fetchTableData = leadId => ({
    current,
    pageSize,
}) =>
    service.get(apiMap.markData.path, {
        pageNum: current,
        pageSize,
        leadId,
    })
        .then(res => res.data)
        .then(data => (
            {
                total: data.total,
                list: data.logs.sort((a, b) => b.recordTime - a.recordTime),
            }))
        .catch((err) => {
            message.error(err);
        });

const MarkTable = () => {
    const {
        leadId,
        leadDetail,
    } = useContext(LeadDetailContext);
    const { tableProps } = useAntdTable(fetchTableData(leadId), {
        refreshDeps: [leadDetail],
    });

    return <Table diffId='markTable' columns={markCols} rowKey={record => JSON.stringify(record)} {...tableProps} />;
};

export default MarkTable;
