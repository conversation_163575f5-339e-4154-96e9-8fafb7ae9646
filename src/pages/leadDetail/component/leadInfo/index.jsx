/* eslint-disable react/prop-types,import/extensions,import/no-unresolved */

import Info from '@/components/info';
import React, { useContext } from 'react';
import { Button } from 'antd';
import { bellwetherLinkParse } from '@mfe/bellwether-route';
import { apiMap } from '../../../../common/service/apiMap';
import SearchImage from './searchImage';
import UploadImgModal from './uploadImgModal';
import { AI_FOOLOW_STATUS_MAP } from "../../../leadList/constant";

import {
    getHost,
    timeRender,
} from '../../../../utils/utils';
import LeadDetailContext from '../../context/leadDetailContext';
import useFetchData from '../../hooks/useFetchData';

const spliceParameters = parameters => Object.keys(parameters)
    .reduce((pre, key) => `${pre + key}=${parameters[key]}&`, '')
    .slice(0, -1);

const DEFAULT_VALUE = '-';
const leadInfoCols = [
    {
        label: '线索ID',
        prop: 'leadId',
    },
    {
        label: 'WDC ID',
        prop: 'wdcId',
        defaultValue: DEFAULT_VALUE,
    },
    {
        label: '流入时间',
        prop: 'intoTime',
        render: timeRender,
    }, {
        label: '最近外呼时间',
        prop: 'latestCallTime',
        render: time => (
            time ? timeRender(time) : '-'),
    },
    {
        label: '渠道来源',
        prop: 'channelSourceDesc',
        render: (text, row) => (
            <div style={{ display: 'flex' }}>
                <span>{text}</span>
                {
                    row.channelSource === 10 ? <div style={{ marginLeft: 20, width: 300 }}>
                        <SearchImage bizId={row.wdcId} />
                    </div> : null
                }

            </div>),
    },
    {
        label: '当前状态',
        prop: 'statusDesc',
    },

    {
        label: '最新标签',
        prop: 'latestLabelInfo',
        defaultValue: DEFAULT_VALUE,
    },

    {
        label: '跟进人',
        prop: 'follower',
        render: (follower) => {
            const {
                auth,
                leadId,
                permissions,
                leadDetail,
            } = useContext(LeadDetailContext);
            const [wdcParameters] = useFetchData(apiMap.wdcParameters.path, { newSignLeadId: leadId });

            const poiCreateTarget = getHost();
            // 存在wdcId代表已有门店
            const showBuildPoiBtn = permissions?.buildPoi && leadDetail?.channelSourceDesc !== 'CRM'
                && !leadDetail?.wdcId;

            const disabled = leadDetail?.status !== 3; // 待三次跟进

            return (
                <React.Fragment>
                    <span className="tx-text">{follower || DEFAULT_VALUE}</span>
                    {!!showBuildPoiBtn && auth && auth.uiControl.newsign_buildPoi &&
                        <Button
                            style={{ marginLeft: 5 }}
                            disabled={disabled}
                            type="primary"
                            href={bellwetherLinkParse(`${poiCreateTarget}?${wdcParameters ? spliceParameters(wdcParameters) : ''}`)}
                            target="_blank"
                        >
                            建店
                        </Button>}
                </React.Fragment>);
        },
    },

    {
        label: '辅助跟进人',
        prop: 'auxFollower',
        defaultValue: DEFAULT_VALUE,
    },

    {
        label: '菜单状态',
        prop: 'menuInfo',
        render: (info) => {
            const {
                auth,
                leadId,
                permissions,
                leadDetail,
                menuRefresh,
            } = useContext(LeadDetailContext);

            // 存在wdcId代表已有门店
            const showBuildPoiBtn = permissions?.buildPoi;

            const disabled = ![4, 3].includes(leadDetail?.status) || leadDetail?.wmPoiOrderStatus === 1; // 待三次跟进 或者成功

            return (
                <React.Fragment >
                    <span className="tx-text mr-10">{info ? '已提交' : '未提交'}</span>
                    {!!showBuildPoiBtn && auth && auth.uiControl.newsign_buildPoi &&
                        <UploadImgModal
                            leadId={leadId}
                            onConfirm={() => {
                                menuRefresh();
                            }}
                            oncCancel={() => {

                            }}
                            disabled={disabled}
                            menuInfo={info}
                            btnText={'选择farmer，上传菜单'}
                        />
                    }
                </React.Fragment >);
        },
    },
];

const claimName = 'claimName';
const claimMisId = 'claimMisId';
const transform = (data, menuInfo) => data && (
    {
        ...data,
        follower: data?.claimMisId ? `${data[claimName]}(${data[claimMisId]})` : '-',
        menuInfo,
    });

const LeadInfo = ({ data, menuInfo }) =>
    <div className="lead-base-info">
        <Info title="线索信息" data={transform(data, menuInfo)} cols={leadInfoCols} />
        <div className='agent-follow-status'>
            {AI_FOOLOW_STATUS_MAP[data?.aiStatus] || AI_FOOLOW_STATUS_MAP[1]}
        </div>
    </div>
export default LeadInfo;
