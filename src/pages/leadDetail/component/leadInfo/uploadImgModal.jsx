import React, { useEffect, useState, useRef } from 'react';
import { Input, message, Upload, Modal, Spin, Button, Form, Alert } from 'antd';
import { PlusOutlined, CloseOutline } from '@ant-design/icons';
import { cloneDeep, debounce } from 'lodash';
import getPrefix from '../../../../common/service/getPrefix';
import service from '../../../../common/service';
// import { downloadImage } from '../../../../utils/utils';
import './index.scss';

export default function UploadImgModal(props) {
    const {
        leadId, menuInfo, btnText, onCancel, onConfirm, disabled,
    } = props;

    const [previewOpen, setPreviewOpen] = useState(false);
    const [previewImage, setPreviewImage] = useState('');
    const [visible, setVisible] = useState();
    const [loading, setLoading] = useState(false);
    const [imageLen, setImageLen] = useState(0);
    const [urlMap, setUrlMap] = useState({});
    const [form] = Form.useForm();
    const [checkImgResult, setCheckImgResult] = useState([]);
    const [showImgErrorTips, setShowImgErrorTips] = useState(false);
    useEffect(() => {
        if (menuInfo && visible) {
            if (menuInfo.pictureUrls?.length) {
                // 图片转化 相对路径图片转化mss地址
                service.post(service.apiMap.uploadInfoConvert.path, menuInfo.pictureUrls).then((res) => {
                    if (res.code === 0) {
                        // const urls = Object.values(res.data);
                        const urls = menuInfo.pictureUrls.map(item => {
                            return {
                                url: res.data[item]
                            }
                        })
                        setUrlMap(res.data);
                        form.setFieldsValue({
                            pictureUrls: urls,
                        });
                        setImageLen(urls.length || 0);
                    }
                });
            }
            form.setFieldsValue({
                remark: menuInfo.remark || '',
            });
        } else {
            form.resetFields();
            setImageLen(0);
            setUrlMap({});
            setShowImgErrorTips(false);
            setCheckImgResult([]);
        }
    }, [menuInfo, visible]);

    const setErrorImageResult = (res) => {
        if (res.filter(f => !f.pass).length) {
            const result = [];
            res.forEach((item, index) => {
                if (!item.pass) {
                    result.push(index + 1);
                }
            })
            setCheckImgResult(result);
            setShowImgErrorTips(true);
        } else {
            setCheckImgResult([]);
            setShowImgErrorTips(false);
        }
    }

    const imagesCheck = async (images) => {

        // 拷贝一份images信息
        const imgs = cloneDeep(images);
        const needConvertPath = [];

        imgs.forEach((item, index) => {
            if (item.url.indexOf('/download/mos') > -1) {
                needConvertPath.push({
                    url: item.url,
                    index
                });
            }
        })

        // 转换 url
        if (needConvertPath.length) {
            const res = await service.post(service.apiMap.uploadInfoConvert.path, needConvertPath.map(item => item.url));
            // then((res) => {
            if (res.code === 0) {
                Object.keys(res.data || {}).forEach((key) => {
                    const fullPath = res.data[key];
                    imgs.filter(ker => {
                        return ker.url === key;
                    }).forEach(img => {
                        img.url = fullPath;
                    })
                })
            }
        }
        if (imgs.length) {
            const param = {
                urlList: imgs.map(item => item.url),
                entityId: leadId,
                entityIdType: 4,
                picSource: 4
            }

            // 检查
            service.post('/api/farmer/decoration/visual/menuPicCheck', param).then((checkResult) => {
                if (checkResult.code === 0) {
                    setErrorImageResult(checkResult.data);
                }
            });
        } else {
            setErrorImageResult([]);
        }
        // })
    }

    return (
        <div >
            <Spin spinning={loading}>
                <Modal
                    title="上传图片"
                    width={1000}
                    open={visible}
                    closable={false}
                    maskClosable={false}
                    okButtonProps={{ disabled: showImgErrorTips }}
                    onOk={() => {
                        form.validateFields().then((v) => {
                            const pictureUrls = [];
                            v.pictureUrls?.forEach((item) => {
                                if (item.response?.code === 0) {
                                    pictureUrls.push(item.response.data);
                                } else if (item.url) {
                                    // 全地址转回相对地址
                                    const relativeUrl = Object.keys(urlMap).find(key => urlMap[key] === item.url);
                                    if (relativeUrl) {
                                        pictureUrls.push(relativeUrl);
                                    }
                                }
                            });
                            const params = {
                                remark: v.remark,
                                pictureUrls,
                            };
                            service.post(service.apiMap.uploadInfoSave.path, { leadId, ...params }).then((res) => {
                                if (res.code === 0) {
                                    setVisible(false);
                                    message.success('已成功提交');
                                    if (onConfirm) {
                                        onConfirm();
                                    }
                                } else {
                                    message.error(res.msg || '保存失败');
                                }
                            }).finally(() => {
                                setLoading(false);
                            });
                        });
                    }}
                    onCancel={() => {
                        setVisible(false);
                    }}
                >

                    <Form
                        form={form}
                        name="form"
                        layout="vertical"
                        className="uploadImgForm"
                        onValuesChange={(changedValues, values) => {
                            setImageLen(values.pictureUrls?.length || 0);
                        }}
                    >
                        {showImgErrorTips ? <Alert
                            style={{ marginBottom: 10 }}
                            description={`当前菜单图片第${checkImgResult.join(',')}张不合格，请上传新的菜单图片，以免耽误您及时入驻美团平台。`}
                            type="error"

                        /> : null}

                        <Form.Item
                            name="pictureUrls"
                            label={<div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                                <div>菜单上传 <span style={{ color: 'gray' }}>不超过60张，每张不超过5M</span></div>
                                <span
                                    style={{ color: 'gray', cursor: 'pointer' }}
                                    onClick={(e) => {
                                        form.setFieldValue('pictureUrls', []);
                                        setErrorImageResult([]);
                                        setShowImgErrorTips(false);
                                        e.stopPropagation();
                                    }}
                                >一键清空</span>

                            </div>}
                            required
                            valuePropName="fileList"
                            getValueFromEvent={value => value.fileList}
                            rules={[{
                                validator(_, value) {
                                    if (value && value?.length) {
                                        return Promise.resolve();
                                    } else {
                                        return Promise.reject(new Error('请上传菜单图片'));
                                    }
                                },
                            }]}

                            htmlFor="default_pictureUrls"

                        >
                            <Upload
                                action={`${getPrefix(service.apiMap.upload.path)}${service.apiMap.upload.path}`}
                                listType="picture-card"
                                multiple
                                maxCount={60}
                                beforeUpload={(res) => {
                                    if (res && res.size > 1024 * 1024 * 5) {
                                        message.error('图片大小不能超过5M');
                                        return Upload.LIST_IGNORE;
                                    }

                                    if (!['image/png', 'image/jpg', 'image/jpeg'].includes(res.type)) {
                                        message.error('只能上传后缀为.jpg,.png,.jpeg图片');
                                        return Upload.LIST_IGNORE;
                                    }
                                }}
                                onPreview={(file) => {
                                    setPreviewImage(file.thumbUrl || file.url);
                                    setPreviewOpen(true);
                                }}

                                onChange={debounce(({ file, fileList }) => {
                                    if (['done', 'removed'].includes(file.status)) {
                                        const images = fileList.map(item => {
                                            return {
                                                url: item?.response?.data || item.url
                                            }
                                        })
                                        imagesCheck(images);
                                    }
                                }, 300)}
                            >
                                {
                                    imageLen > 59 ? null : <div>
                                        <PlusOutlined />
                                        <div
                                            style={{
                                                marginTop: 8,
                                            }}
                                        >
                                            上传
                                        </div>
                                    </div>
                                }
                            </Upload>
                        </Form.Item>

                        <Form.Item name="remark" label="备注" >
                            <Input.TextArea showCount maxLength={500} rows={3} />
                        </Form.Item>
                    </Form>

                    <Modal open={previewOpen} footer={null} onCancel={() => setPreviewOpen(false)}>
                        <img alt="example" style={{ width: '100%' }} src={previewImage} />
                    </Modal>
                </Modal>
                <Button
                    style={{ borderRadius: 5 }}
                    disabled={disabled}
                    type="primary"
                    onClick={() => {
                        setVisible(true);
                    }}
                >{btnText}</Button>
            </Spin>
        </div >
    );
}
