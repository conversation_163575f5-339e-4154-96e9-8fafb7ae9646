import React, { useState } from 'react';
import { Image, Button, Spin } from 'antd';
import service from '../../../../common/service';
import { apiMap } from '../../../../common/service/apiMap';
import { downloadImage } from '../../../../utils/utils';

export default function SearchImage(props) {
    const { bizId } = props;
    const [images, setImages] = useState({});
    const [loading, setLoading] = useState(false);
    return (
        <div >
            <Spin spinning={loading}>
                <Button
                    onClick={() => {
                        setLoading(true);
                        service.get(apiMap.getBulkCertInfo.path, { wdcId: bizId })
                            .then((res) => {
                                setLoading(false);

                                if (res.code === 0) {
                                    setImages(res.data || {});
                                }
                            }).catch(() => {
                                setLoading(false);
                            });
                    }}
                >资质查询</Button>
                {
                    Object.keys(images) && Object.keys(images).length ? <div style={{ width: 100 }} className="flex mt-10">
                        {
                            Object.keys(images).map(item => (
                                <div className="mr-10">
                                    <Image
                                        width={100}
                                        src={images[item]}
                                    />
                                    <div style={{ position: 'relative' }}>
                                        <span className="fs-14">{item}</span>
                                        <span
                                            className="ml-5"
                                            style={{ color: '#3C99D8', fontSize: 14, cursor: 'pointer' }}
                                            onClick={() => {
                                                downloadImage(images[item], item);
                                            }}
                                        >下载</span>
                                    </div>
                                </div>))
                        }
                    </div> : null
                }
            </Spin>
        </div >
    );
}
