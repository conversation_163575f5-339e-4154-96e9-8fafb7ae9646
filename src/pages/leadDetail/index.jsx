import { getUrlParam } from '@wmfe/util_param';
import { useRequest } from 'ahooks';
import { ConfigProvider, message, Spin } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import React, { useEffect, useMemo, useState } from 'react';
import Owl from '@dp/owl';
import '../../utils/sso-web';

import { authCreate } from '../../utils/utils';
import '../../assets/styles/index.scss';
import '../../assets/styles/leadDetail.scss';

import service from '../../common/service';
import { apiMap } from '../../common/service/apiMap';
import LeadDetailContext from './context/leadDetailContext';
import useFetchData from './hooks/useFetchData';
import LeadDetail from './leadDetail';
import { BizEnum } from '../../utils/const';
import { renderMicroApp } from '../../renderMicroApp';

// 接入 OWL
Owl.start({
    project: 'com.sankuai.wmmerchantfront.tmk.clue',
    pageUrl: 'leadDetail',
    devMode: window.location.href.indexOf('igate.waimai.test.sankuai.com') > -1,
    resource: {
        sampleApi: 1,
    },
    page: {
        sample: 1,
    },
});

const bizId = getUrlParam('bizId') || '';

const useBizManage = () => {
    const [list, setList] = useState([]);
    const [activeBizId, setActiveBizId] = useState('');
    const [auth, setAuth] = useState(null);

    useEffect(() => {
        service
            .get('/api/newSign/newSignLead/auth/getRole')
            .then(res => {
                if (res.code === 0 && res.data) {
                    let newList =
                        (res.data || []).filter(item => item.hasPermission) ||
                        [];
                    if (newList.length === 0) {
                        message.error('请先配置业务线');
                        return;
                    }

                    // 特殊逻辑 后面估计新签不会怎么维护了。乱了
                    const qyTenant = newList.find(
                        item => item.tenantId === BizEnum.QY,
                    );
                    if (qyTenant) {
                        qyTenant.roleMap = {
                            ...qyTenant.roleMap,
                            is_admin: qyTenant.roleMap.is_enterprise_admin,
                            is_business:
                                qyTenant.roleMap.is_enterprise_business,
                        };
                    }

                    setList(newList);
                    let biz = null;
                    if (bizId) {
                        biz = newList.find(item => item.tenantId === +bizId);
                        if (!biz) {
                            message.error('业务线权限异常');
                            return;
                        }
                    } else {
                        [biz] = newList;
                    }

                    setActiveBizId(biz.tenantId);
                    setAuth(authCreate(biz.roleMap));
                }
            })
            .catch(() => {
                message.error('业务线获取异常');
            });
    }, []);
    return [list, activeBizId, auth];
};

const useMenuInfo = leadId => {
    const [data, setData] = useState(null);
    const [refresh, setRefresh] = useState(0);

    useEffect(() => {
        service
            .get(service.apiMap.uploadInfoQuery.path, { leadId })
            .then(res => {
                if (res.code === 0 && res.data) {
                    setData(res.data);
                }
            })
            .catch(() => {});
    }, [refresh]);
    return [
        data,
        () => {
            setRefresh(refresh + 1);
        },
    ];
};

const Root = () => {
    const [visible, setVisible] = useState(false);
    const [doveCallId, setDoveCallId] = useState(null);
    const [, , auth] = useBizManage();
    const leadId = getUrlParam('leadId', document.location.href);
    const [menuInfo, menuRefresh] = useMenuInfo(leadId);

    const {
        data: leadDetail,
        run: fetchDetail,
        refresh: refreshDetail,
        loading,
    } = useRequest(
        () =>
            service
                .get(apiMap.leadDetail.path, { leadId })
                .then(res => {
                    if (res?.code !== 0) {
                        message.error('线索详情获取失败');
                    }
                    return res;
                })
                .then(res => res?.data)
                .catch(() => message.error('线索详情获取失败')),
        {
            manual: true,
        },
    );
    window.__LeadDetail__ = leadDetail;

    const refresh = () => setTimeout(() => refreshDetail(), 1500);

    useEffect(() => {
        fetchDetail();
    }, []);

    const [permissions] = useFetchData(apiMap.permission.path, {
        leadId,
    });

    const [optionInfo] = useFetchData(apiMap.optionInfo.path, {
        bizId,
    });
    const [poiId] = useFetchData(apiMap.poiId.path);
    const callBtnDisabled = useMemo(
        () =>
            leadDetail?.status === 4 ||
            leadDetail?.status === 5 ||
            leadDetail?.status === -1 ||
            leadDetail?.status === -2,
        [leadDetail],
    );
    const callBtnWxDisabled = useMemo(
        () => leadDetail?.status === -1 || leadDetail?.status === -2,
        [leadDetail],
    );

    return (
        <ConfigProvider locale={zhCN}>
            <LeadDetailContext.Provider
                value={{
                    auth,
                    visible,
                    setVisible,
                    leadId,
                    leadDetail,
                    doveCallId,
                    setDoveCallId,
                    permissions,
                    poiId,
                    callBtnDisabled,
                    callBtnWxDisabled,
                    refresh,
                    optionInfo: optionInfo || {},
                    menuInfo,
                    menuRefresh,
                }}
            >
                <Spin spinning={loading}>
                    <LeadDetail />
                </Spin>
            </LeadDetailContext.Provider>
        </ConfigProvider>
    );
};

const renderApp = () => {
    renderMicroApp(() => <Root />);
};

renderApp();

const hot = 'hot';
if (process.env.NODE_ENV === 'development') {
    if (module[hot]) {
        module[hot].accept();
    }
}
