@import "../../assets/styles/common.scss";

// 编辑门店信息 标题
.editor-div-names {
    display: flex;
    justify-content: space-between;
}

// 弹框
.contactPhone-div-add {
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.contactPhone-name2 {
    .ant-form-item-label>label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
        display: none;
    }
}

.new-clues-modal-name>.ant-modal-content>.ant-modal-footer {
    text-align: center;
}

.ant-table-thead>tr>th {
    font-size: 14px;
}

.ant-table-tbody>tr>td {
    font-size: 14px;
}

// 
dd {
    margin-left: 0;
    margin-bottom: 0.5rem;
}