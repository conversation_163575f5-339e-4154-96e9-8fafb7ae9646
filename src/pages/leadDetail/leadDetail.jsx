/* eslint-disable react/prop-types,import/extensions,import/no-unresolved */

import Wrapper from '@/components/wrapper';
import {
    QuestionCircleOutlined,
    UnorderedListOutlined,
} from '@ant-design/icons';
import '@roo/roo/dist/theme/blue/index.css';
import { bellwetherLinkParse } from '@mfe/bellwether-route';

import {
    Breadcrumb,
    Card,
    Divider,
    Tabs,
    Tooltip,
} from 'antd';
import { getUrlParam } from '@wmfe/util_param';
import { DifferentItem, Different } from '@mfe/diff-react';

// import 'antd/dist/antd.css';
import React, { useContext, useState } from 'react';
import { getCluePath } from '../../utils/utils';
import PrimaryButton from '../../components/primaryButton';
import LeadInfo from './component/leadInfo';
import MarkModal from './component/markModal';
import MarkTable from './component/markTable';
import WeChatModal from './component/weChatModal';
import OpTable from './component/opTable';
import StoreInfo from './component/storeInfo';
import LeadDetailContext, { useModal } from './context/leadDetailContext';
import './index.scss';
import { BizEnum } from '../../utils/const';
import { getDiffConfig } from '../../diff/different';

const bizId = getUrlParam('bizId') || '';

const TablesTab = ({
    auth, openModal, showLabelBtn, callBtnDisabled, callBtnWxDisabled,
}) => {
    const {
        leadDetail,
        refresh,
    } = useContext(LeadDetailContext);

    const [visible, setVisible] = useState(false);
    return (<Wrapper relative noPadding>
        <Tabs>
            <Tabs.TabPane tab="打标和外呼记录" key="打标记录">
                <MarkTable />
            </Tabs.TabPane>
            <Tabs.TabPane tab="操作记录" key="操作记录">
                <OpTable />
            </Tabs.TabPane>
        </Tabs>


        {showLabelBtn && auth && auth.uiControl && auth.uiControl.newsign_label &&
            <div className="lead-detail-absolute lead-detail-flex lead-detail-align-center">
                <DifferentItem diffId='WeChatBinding'>
                    <PrimaryButton
                        className="lead-detail-mr-sm"
                        onClick={() => {
                            setVisible(true);
                        }}
                        disabled={callBtnWxDisabled}
                    >
                        企微绑定
                    </PrimaryButton>
                </DifferentItem>

                <PrimaryButton
                    className="lead-detail-mr-sm"
                    onClick={openModal}
                    disabled={BizEnum.QY !== +bizId ? callBtnDisabled : [1, 0].includes(leadDetail?.status)}
                >
                    线索打标
                </PrimaryButton>
                {
                    (BizEnum.QY !== +bizId ? callBtnDisabled : false) &&
                    <Tooltip title="该线索已闭环,无法打标">
                        <QuestionCircleOutlined style={{
                            fontSize: 20,
                            color: 'gray',
                        }}
                        />
                    </Tooltip>
                }
            </div>}

        <WeChatModal
            visible={visible}
            data={leadDetail}
            bizId={bizId}
            onCancel={() => {
                setVisible(false);
            }}
            onConfirm={() => {
                setVisible(false);
                refresh();
            }}
        />
    </Wrapper>);
};

const LeadDetail = () => {
    const {
        visible,
        auth,
        leadDetail,
        permissions,
        callBtnDisabled,
        callBtnWxDisabled,
        menuInfo,
        menuRefresh,
    } = useContext(LeadDetailContext);
    const [hideModal, openModal] = useModal();
    return (
        <Different data={getDiffConfig(bizId)}>
            <React.Fragment>
                <Card>
                    <Breadcrumb>
                        <Breadcrumb.Item key="电销新签工作台"><a href={bellwetherLinkParse(`${getCluePath()}/clue/leadList.html`)}>电销新签工作台</a></Breadcrumb.Item>
                        <Breadcrumb.Item key="线索详情" className="lead-detail-tx-blue">线索详情</Breadcrumb.Item>
                    </Breadcrumb>
                    <Wrapper gray className="lead-detail-pd-sm lead-detail-mt-sm lead-detail-mb-sm">
                        <UnorderedListOutlined />
                        线索详情
                    </Wrapper>

                    <LeadInfo data={leadDetail} auth={{ ...auth }} menuInfo={menuInfo} />
                    <Divider />

                    <StoreInfo data={leadDetail} auth={{ ...auth }} />
                    <Divider />

                    <TablesTab auth={{ ...auth }} openModal={openModal} showLabelBtn={permissions?.label} callBtnDisabled={callBtnDisabled} callBtnWxDisabled={callBtnWxDisabled} />
                </Card>
                <MarkModal visible={visible} hideModal={hideModal} data={leadDetail} bizId={bizId} />
            </React.Fragment>
        </Different>
    );
};

export default LeadDetail;
