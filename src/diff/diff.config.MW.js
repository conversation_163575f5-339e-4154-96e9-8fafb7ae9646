import { ConfigAssist } from '@mfe/diff-next';
import { FOLLOW_STATUS } from '../utils/const';
const { Visible, Options, MultiProperty, Property, Text } = ConfigAssist;
export const config = [
    {
        "name": "leadId",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "customerId",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "customerName",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "customerType",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "leadName",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },

    {
        "name": "cityIds",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "orgIds",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "signer",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "labelTime",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "statusList",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "statusList",
        "result": [
            {
                "value": [
                    {
                        "label": "全部",
                        "value": "-1"
                    },
                    {
                        "label": "待分配",
                        "value": "-3,-2,-1,0"
                    },
                    {
                        "label": "待首次跟进",
                        "value": "1"
                    },
                    {
                        "label": "待二次跟进",
                        "value": "2"
                    },
                    {
                        "label": "待三次跟进",
                        "value": "3"
                    },
                    {
                        "label": "已闭环",
                        "value": "4,5"
                    }
                ]
            }
        ],
        "type": 1
    },
    {
        "name": "categoryId",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "phoneNumber",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "infoTime",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "claimUid",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "auxFollowerUid",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "endType",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "lastLabelIds",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "orgId",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "orderStatus",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "aiStatusList",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "kpLevel",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "enterpriseScale",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "openingPlanning",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "cooperationDemand",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "hopeCity",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "incomeRange",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "clueImportButton",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "table",
        "result": [
            {
                "value": [
                    {
                        "label": "线索ID",
                        "value": "leadId"
                    },
                    {
                        "label": "门店名称",
                        "value": "leadName"
                    },
                    {
                        "label": "上单状态",
                        "value": "wmPoiOrderStatus"
                    },
                    {
                        "label": "门店品类",
                        "value": "categoryName"
                    },
                    {
                        "label": "物理城市",
                        "value": "cityName"
                    },
                    {
                        "label": "渠道来源",
                        "value": "channelSourceDesc"
                    },
                    {
                        "label": "流入时间",
                        "value": "intoTime"
                    },
                    {
                        "label": "跟进状态",
                        "value": "statusDesc"
                    },
                    {
                        "label": "跟进人",
                        "value": "claimName"
                    },
                    {
                        "label": "备注",
                        "value": "labelRemarks"
                    },
                    {
                        "label": "最新标签",
                        "value": "latestLabelInfo"
                    },
                    {
                        "label": "操作",
                        "value": "operation"
                    }
                ]
            }
        ],
        "type": 1
    },
    {
        "name": "wdcId",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "intoTime",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "latestCallTime",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "channelSourceDesc",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "statusDesc",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "latestLabelInfo",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "follower",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "auxFollower",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "menuInfo",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "customerPois",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "categoryName",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "cityName",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "address",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "phoneList",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "qwPhoneList",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "kpInfoDTOS",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "title",
        "result": [
            {
                "value": "门店信息"
            }
        ],
        "type": 2
    },
    {
        "name": "WeChatBinding",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "firstLevel",
        "result": [
            {
                "value": "跟进结果"
            }
        ],
        "type": 2
    },
    {
        "name": "secondLevel",
        "result": [
            {
                "value": "标签类型"
            }
        ],
        "type": 2
    },
    {
        "name": "visitType",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "thirdLevel",
        "result": [
            {
                "value": "具体内容"
            }
        ],
        "type": 2
    },
    {
        "name": "markTable",
        "result": [
            {
                "value": [
                    {
                        "label": "操作人",
                        "value": "operatorDesc"
                    },
                    {
                        "label": "线索状态",
                        "value": "statusDesc"
                    },
                    {
                        "label": "通话记录",
                        "value": "callRecordUrl"
                    },
                    {
                        "label": "外呼开始时间",
                        "value": "callRecordStartTime"
                    },
                    {
                        "label": "外呼结束时间",
                        "value": "callRecordEndTime"
                    },
                    {
                        "label": "跟进结果",
                        "value": "labelProcess"
                    },
                    {
                        "label": "标签类型",
                        "value": "labelType"
                    },
                    {
                        "label": "具体内容",
                        "value": "labelContent"
                    },
                    {
                        "label": "记录时间",
                        "value": "recordTime"
                    }
                ]
            }
        ],
        "type": 1
    }
]
