import { ConfigAssist } from '@mfe/diff-next';
import { FOLLOW_STATUS_QY } from '../utils/const';
const { Visible, Options, MultiProperty, Property, Text } = ConfigAssist;
export const config = [
    {
        "name": "leadId",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "customerId",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "customerName",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "customerType",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "leadName",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "cityIds",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "signer",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "labelTime",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "statusList",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "statusList",
        "result": [
            {
                "value": [
                    {
                        "label": "全部",
                        "value": "-1"
                    },
                    {
                        "value": "1",
                        "label": "待分配"
                    },
                    {
                        "value": "2",
                        "label": "已触达"
                    },
                    {
                        "value": "3",
                        "label": "对账中"
                    },
                    {
                        "value": "4",
                        "label": "开具中"
                    },
                    {
                        "value": "5",
                        "label": "已开具"
                    },
                    {
                        "value": "6",
                        "label": "已倒闭"
                    }
                ]
            }
        ],
        "type": 1
    },
    {
        "name": "categoryId",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "phoneNumber",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "infoTime",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "claimUid",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "auxFollowerUid",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "endType",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "lastLabelIds",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "orgId",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "orderStatus",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "kpLevel",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "enterpriseScale",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "openingPlanning",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "cooperationDemand",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "hopeCity",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "incomeRange",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "clueImportButton",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "table",
        "result": [
            {
                "value": [
                    {
                        "label": "线索ID",
                        "value": "leadId"
                    },
                    {
                        "label": "客户ID",
                        "value": "customerId"
                    },
                    {
                        "label": "客户名称",
                        "value": "customerName"
                    },
                    {
                        "label": "客户类型",
                        "value": "customerType"
                    },
                    {
                        "label": "关联门店数量",
                        "value": "customerPoiCount"
                    },
                    {
                        "label": "签约人",
                        "value": "signer"
                    },
                    {
                        "label": "流入时间",
                        "value": "intoTime"
                    },
                    {
                        "label": "跟进状态",
                        "value": "statusDesc"
                    },
                    {
                        "label": "跟进人",
                        "value": "claimName"
                    },
                    {
                        "label": "辅助跟进人",
                        "value": "auxFollowerMis"
                    },
                    {
                        "label": "备注",
                        "value": "labelRemarks"
                    },
                    {
                        "label": "操作",
                        "value": "operation"
                    }
                ]
            }
        ],
        "type": 1
    },
    {
        "name": "wdcId",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "intoTime",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "latestCallTime",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "channelSourceDesc",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "statusDesc",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "latestLabelInfo",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "follower",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "auxFollower",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "menuInfo",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "customerPois",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "categoryName",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "cityName",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "address",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "phoneList",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "qwPhoneList",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "kpInfoDTOS",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "title",
        "result": [
            {
                "value": "客户信息"
            }
        ],
        "type": 2
    },
    {
        "name": "WeChatBinding",
        "result": [
            {
                "value": false
            }
        ],
        "type": 0
    },
    {
        "name": "firstLevel",
        "result": [
            {
                "value": "跟进状态"
            }
        ],
        "type": 2
    },
    {
        "name": "secondLevel",
        "result": [
            {
                "value": "拜访事项"
            }
        ],
        "type": 2
    },
    {
        "name": "visitType",
        "result": [
            {
                "value": true
            }
        ],
        "type": 0
    },
    {
        "name": "thirdLevel",
        "result": [
            {
                "value": "拜访结果"
            }
        ],
        "type": 2
    },
    {
        "name": "markTable",
        "result": [
            {
                "value": [
                    {
                        "label": "操作人",
                        "value": "operatorDesc"
                    },
                    {
                        "label": "线索状态",
                        "value": "statusDesc"
                    },
                    {
                        "label": "通话记录",
                        "value": "callRecordUrl"
                    },
                    {
                        "label": "外呼开始时间",
                        "value": "callRecordStartTime"
                    },
                    {
                        "label": "外呼结束时间",
                        "value": "callRecordEndTime"
                    },
                    {
                        "label": "跟进状态",
                        "value": "labelProcess"
                    },
                    {
                        "label": "拜访方式",
                        "value": "visitType"
                    },
                    {
                        "label": "拜访事项",
                        "value": "labelType"
                    },
                    {
                        "label": "拜访结果",
                        "value": "labelContent"
                    },
                    {
                        "label": "记录时间",
                        "value": "recordTime"
                    }
                ]
            }
        ],
        "type": 1
    }
]