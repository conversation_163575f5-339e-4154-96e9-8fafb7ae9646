import React from 'react';
import { config as wmConfig } from './diff.config.MW';
import { config as sgConfig } from './diff.config.SG';
import { config as qyConfig } from './diff.config.QY';
import { BizEnum } from '../utils/const';

// 初始化差异配置文件
export const getDiffConfig = (bizId) => {
    return {
        [`${BizEnum.WM}`]: wmConfig,
        [`${BizEnum.SG}`]: sgConfig,
        [`${BizEnum.QY}`]: qyConfig,
    }[bizId] || wmConfig
};

