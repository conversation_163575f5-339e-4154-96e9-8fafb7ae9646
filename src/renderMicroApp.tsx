import React, { FC, ComponentType } from 'react';
import ReactDOM from 'react-dom';
import { register } from './module/request';

function render(App: FC | ComponentType, id, props?) {
    register();
    const { container } = props || {};
    ReactDOM.render(
        <App />,
        container ? container.querySelector(id) : document.querySelector(id),
    );
}

/**
 * 渲染微应用组件
 * @param App 微应用
 * @param id ID选择器
 * @param path 微前端路径，nine项目统一为‘bellwether_entry’
 */
export const renderMicroApp = (
    App: FC | ComponentType,
    id: string = '#root', //TODO：根据你项目页面实际挂在节点修改默认值
    path: string = 'bellwether_entry',
) => {
    register();
    ((global) => {
        // 注册微应用信息，钩子函数绑定
        global[path] = {
            bootstrap: async () => {},
            mount: async (props) => {
                render(App, id, props);
            },
            unmount: async (props) => {
                const { container } = props;
                // 根据页面实际挂在节点，修改querySelector参数
                ReactDOM.unmountComponentAtNode(
                    container
                        ? container.querySelector(id)
                        : document.querySelector(id),
                );
            },
        };
    })(window);

    // 如果是非微前端环境（子应用单独运行），则直接渲染
    if (!(window as any).__POWERED_BY_QIANKUN__) {
        render(App, id);
    }
};
