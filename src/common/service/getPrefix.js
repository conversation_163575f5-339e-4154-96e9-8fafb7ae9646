const PUBLIC_PATH = process.env.PUBLIC_PATH;// webpack可以通过DefinePlugin注入

const getPrefix = (path, fallback = '') => {

    if (!PUBLIC_PATH) {
        return '';
    }

    // http://panda-in.crm.waimai.sankuai.com
    if (/^\/uicomponent\/employs/.test(path)
        || /^\/uicomponent\/highseas\/cates/.test(path)

    ) {
        return '/xianfu/api/clue/panda';
    }

    // https://highsea.waimai.sankuai.com
    if (/^\/highsea/.test(path)
    ) {
        return '/xianfu/api/clue/highsea';
    }

    // https://farmer.sankuai.com
    if (
        /^\/file/.test(path)
    ) {
        return '/xianfu/api/clue/oldFarmer';
    }

    // https://auth-ai.vip.sankuai.com
    if (/^\/oauth\/v2\/token/.test(path)) {
        return '/xianfu/api/clue/authAI';
    }

    // 语音转文字服务
    if (/^\/asr\/v1\/sentence_recognize/.test(path)
    ) {
        return '/xianfu/api/clue/asr';
    }

    // 获取录音回调
    if (/^\/api\/newSign\/common\/getAsrResult/.test(path)) {
        return '/xianfu/api/clue/asrResult';
    }

    // https://waimai-openapi.vip.sankuai.com
    if (/^\/api\/farmer/.test(path)
        || /^\/api\/newSign/.test(path)
        || /^\/uicomponent\/api\/controlView\/keyInfo/.test(path)
    ) {
        return '/xianfu/api/clue/openapi';
    }

    // https://dovecallapi-m.vip.sankuai.com
    if (/^\/dovecall-web\/agentCall/.test(path)) {
        return '/xianfu/api/clue/dovecallapi';
    }

    return fallback;
};
export default getPrefix;
