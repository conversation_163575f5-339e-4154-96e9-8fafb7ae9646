import { apiMap } from './apiMap';
import {
    get as fetchDataGet,
    post as fetchDataPost,
} from './fetchData';
import {
    monitorFilter,
    parseResponseFilter,
} from './filters';
import Pipe from './Pipe';

const pipe = new Pipe();
pipe.use(monitorFilter);
pipe.use(parseResponseFilter);

export default {
    apiMap,
    get(url, params) {
        if (typeof url !== 'string') {
            throw new Error('url 参数错误');
        }
        return pipe.run(
            fetchDataGet({
                url,
                params,
            }),
            url,
        );
    },
    post(url, params, config = {}) {
        if (typeof url !== 'string') {
            throw new Error('url 参数错误');
        }
        return pipe.run(
            fetchDataPost({
                url,
                params,
                contentType: 'application/json',
                ...config,
            }),
            url,
        );
    },
};
