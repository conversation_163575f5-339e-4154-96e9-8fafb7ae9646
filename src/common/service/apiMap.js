const prefix = '/api/newSign';
export const apiMap = {
    /**
     * 公共接口
     */
    getData: {
        path: '/uicomponent/getUserOrgIds',
    },
    getCates: {
        path: '/uicomponent/highseas/cates',
    },
    getCitys: {
        path: '/api/farmer/decoration/poiConfig/cities',
    },
    getCityInfo: {
        path: `${prefix}/newSignLead/common/cityInfo`,
    },

    /**
     * leadList页面接口
     */
    getLeadList: {
        path: `${prefix}/newSignLead/search/list`,
    },
    batchAssign: {
        path: `${prefix}/newSignLead/manage/batchAssign`,
    },
    claim: {
        path: `${prefix}/newSignLead/manage/claim`,
    },
    loopClaim: {
        path: `${prefix}/newSignLead/manage/loopClaim`,
    },
    assign: {
        path: `${prefix}/newSignLead/manage/assign`,
    },
    count: {
        path: `${prefix}/newSignLead/list/counts`,
    },
    qyCount: {
        path: `${prefix}/newSignLead/enterprise/list/counts`,
    },
    sources: {
        path: `${prefix}/newSignLead/import/sources`,
    },
    importExcel: {
        path: `${prefix}/newSignLead/import/excel`,
    },
    importResult: {
        path: `${prefix}/newSignLead/import/result`,
    },
    upload: {
        path: '/file/common/upload',
    },
    export: {
        path: `${prefix}/newSignLead/search/export`,
    },
    getCurrentUser: {
        path: `${prefix}/newSignLead/common/getCurrentUser`,
    },
    getRole: {// is_business:电销人员；is_hq:总部；is_admin:管理员
        path: `${prefix}/newSignLead/auth/getRole`,
    },
    addNewClues: {
        path: `${prefix}/newSignLead/create/create`,
    },
    updataClues: {
        path: `${prefix}/newSignLead/create/update`,
    },
    screenLabel: {
        path: `${prefix}/newSignLead/label/screen`,
    },
    getAreaConfig: {
        path: `${prefix}/newSignLead/common/getAreaConfig`,
    },
    channelSourceInfo: {
        path: `${prefix}/newSignLead/common/channelSourceInfo`,
    },

    /**
     * leadDetail页面
     */
    leadDetail: {
        path: `${prefix}/newSignLead/list/detail`,
    },
    markData: {
        path: `${prefix}/newSignLead/log/label`,
    },
    opData: {
        path: `${prefix}/newSignLead/log/op`,
    },
    wdcParameters: {
        path: `${prefix}/newSignLead/getCreateWdcParams`,
    },
    call: {
        path: `${prefix}/newSignLead/manage/call`,
    },
    decodePhone: {
        path: `${prefix}/newSignLead/phone/decode`,
    },
    alterLabel: {
        path: `${prefix}/newSignLead/label/alter`,
    },
    options: {
        path: `${prefix}/newSignLead/label/list`,
    },
    poiId: {
        path: `${prefix}/newSignLead/common/getCallPoiId`,
    },
    permission: {
        path: `${prefix}/newSignLead/auth/getOperationAuth`,
    },
    optionInfo: {
        path: `${prefix}/newSignLead/common/optionInfo`,
    },
    saveQwInfo: {
        path: `${prefix}/newSignLead/lead/extension/saveQwInfo`,
    },
    queryQwInfo: {
        path: `${prefix}/newSignLead/lead/extension/queryQwInfo`,
    },
    getBulkCertInfo: {
        path: `${prefix}/newSignLead/common/getBulkCertInfo`,
    },
    uploadInfoSave: {
        path: `${prefix}/newSignLead/uploadInfo/save`,
    },
    uploadInfoQuery: {
        path: `${prefix}/newSignLead/uploadInfo/query`,
    },
    uploadInfoConvert: {
        path: `${prefix}/newSignLead/uploadInfo/convert`,
    },
};

export default apiMap;
