import getPrefix from './getPrefix';
/* eslint-disable */
function send(type, options) {
    options = {
        ...options,
        type,
    };

    options.timeout = typeof options.timeout === 'number' ? options.timeout : 5000;

    let abort = null;

    const timerPromise = new Promise((resolve, reject) => {
        abort = () => {
            reject(new TypeError('请求超时，前端中断'));
        };
    });

    setTimeout(() => {
        // abort();
    }, options.timeout);

    return Promise.race([timerPromise, fetchData(options)]);
}


function get(options) {
    if(options.url) {
        options.url =`${getPrefix(options.url)}${options.url}` 
    }
        
    return send('get', options);
}

function post(options) {
    if(options.url) {
        options.url =`${getPrefix(options.url)}${options.url}` 
    }
    return send('post', options);
}


const DEFAULT_OPTIONS = {
    url: '',
    type: 'GET',
    contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
    params: undefined,
    timeout: undefined,

    cache: false,
    getMethodUrlTimestampKey: 'v',
};


function fetchData(options) {
    options = {
        ...DEFAULT_OPTIONS,
        ...options,
    };

    options.type = options.type.toUpperCase();

    (
        options.contentType.indexOf('json') != -1) && (
        options.contentType = 'application/json');

    if (!options.noContent) { // 上传文件的时候，不用运营params
        options.params = serializeParams(options);
    }

    if (options.type == 'GET') {
        options.url += (
            options.params ?
            getUrlConnector(options.url) + options.params :
            ''
        );
        delete options.params;
    }


    const fetchOptions = {
        method: options.type,
        headers: {
            'User-Agent': options.userAgent,
            'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include',
    };

    if (options.type === 'POST') {
        if (!options.noContent) { // 上传文件的时候，如果设置contentType不生效
            fetchOptions.headers['Content-Type'] = options.contentType;
        }
        fetchOptions.body = options.params;
    }
    return fetch(options.url, fetchOptions)
        .then((response) => {
            return response;
        })
        .catch((e) => {
            throw e;
        });
}


function getUrlConnector(url) {
    return url.indexOf('?') == -1 ? '?' : '&';
}


function serializeParams(options) {
    const type = options.type;
    const params = options.params || {};
    const contentType = options.contentType;

    if (type == 'POST' && contentType == 'application/json') {
        return JSON.stringify(params);
    }

    if (type == 'GET' && !options.cache) {
        params[options.getMethodUrlTimestampKey] = +new Date();
    }

    const s    = [],
          add  = function (key, value) {
              s[s.length] = encodeURIComponent(key) + '=' + encodeURIComponent(value);
          },

          loop = function (key, value) {
              if (typeof value == 'object' && value !== null) {
                  if (Object.prototype.toString.call(value) == '[object Object]') {
                      for (var propName in value) {
                          loop(key + '[' + propName + ']', value[propName]);
                      }
                  }

                  if (Object.prototype.toString.call(value) == '[object Array]') {
                      for (var i = 0, l = value.length; i < l; i++) {
                          if (options.traditional) {
                              loop(key, value[i]);
                          } else {
                              loop(key + '[' + i + ']', value[i]);
                          }

                      }
                  }
              } else {
                  // number, string, boolean, null, undefined, function
                  if (value != null && typeof value != 'function') {
                      add(key, value);
                  }
              }
          };

    for (var propName in params) {
        loop(propName, params[propName]);
    }
    return s.join('&');
}


export {
    get,
    post,
};
