/* eslint-disable */
import Owl from '@dp/owl';
import { message } from 'antd';
import { apiMap } from './apiMap';

/**
 * 统计码细分方案
 *
 * -1001 业务正常
 * -1002 后端数据异常（code 非 0 等）
 * -1003 content-type 错误
 * -1004 用户自身网络错误
 * -1005 前端代码错误
 */

function monitorFilter(promise, url) {
    let startTime = new Date().getTime();

    return promise.then((response) => {
        if (response.status >= 200 && response.status < 300 || response.status == 304) {
            const contentType = response.headers.get('content-type');
            if (contentType.indexOf('application/json') !== -1) {
                return response.json()
                    .then((resData) => {
                        const bizStatusValid = validateBizStatus(url, resData);

                        // 业务正常情况
                        if (bizStatusValid) {
                            traceAPINetwork(url, 200, -1001, startTime);
                        } else {
                            // 业务异常情况
                            const extraString = JSON.stringify(resData);
                            traceAPINetwork(url, 200, -1002, startTime, extraString);
                        }

                        return resData;
                    });
            } else {

                const extraString = `content-type: ${contentType}`;
                if (contentType === 'text/plain; charset=utf-8') {
                    return response.text();
                }
                traceAPINetwork(url, 200, -1003, startTime, extraString);
                return response;
            }
        } else {
            traceAPINetwork(url, response.status, undefined, startTime);
            return response;
        }
    })
        .catch((error) => {
            // console.log(error);
            if (error) {
                let errorString;
                if (typeof error === 'object') {
                    errorString =
                        `name: ${error.name || ''}, message: ${error.message || ''}, stack: ${error.stack || ''}`;
                } else {
                    errorString = String(error);
                }

                // 这部分错误不应该统计，这里作为成功情况，进行统计
                if (error.message.indexOf('Failed to fetch') !== -1) {
                    traceAPINetwork(url, 200, -1004, startTime, errorString);
                } else {
                    traceAPINetwork(url, 200, -1005, startTime, errorString);
                }
            }

            throw error;
        });
}

function traceAPINetwork(url, networkCode, statusCode, startTime, extraString = '') {
    // url = `${window.location.protocol}//${window.location.host}${url}`;
    extraString = extraString.slice(0, 300);
    let responseTime = new Date().getTime() - startTime;
    // console.log(
    //     'Trace:',
    //     url,
    //     networkCode,
    //     statusCode,
    //     responseTime + 'ms',
    //     `[${extraString}]`,
    // );

    // 统计上报
    Owl.addApi({
        name: url,
        networkCode,
        statusCode,
        responseTime,
        content: extraString,
    });
}

/**
 * 验证业务状态是否正常
 */
function validateBizStatus(url, resData) {

    if (!resData || typeof resData !== 'object') {
        resData = JSON.parse(resData);
        if (typeof resData !== 'object') {
            return false;
        }
    }
    const bizDefination = getBizDefination(url);
    const resDataCode = Number(resData[bizDefination.bizStatusKey]);
    const resDataMsg = String(resData[bizDefination.bizMsgKey]);

    return bizDefination.bizStatusValue.indexOf(resDataCode) !== -1;

    /**
     * 获取业务成功的定义
     * 包括 key 与 value
     */
    function getBizDefination(url) {
        let ret = {
            bizStatusKey: 'code',
            bizStatusValue: [0],
            bizMsgKey: 'msg',
        };
        Object.keys(apiMap)
            .some((key) => {
                if (apiMap[key].path === url) {
                    ret = {
                        ...ret,
                        ...apiMap[key],
                    };
                    return true;
                }
            });
        // Object.keys(brainMap).some((key) => {
        //     if (brainMap[key].path === url) {
        //         ret = {
        //             ...ret,
        //             ...brainMap[key]
        //         };
        //         return true;
        //     }
        // });
        return ret;
    }
}


function parseResponseFilter(promise, url) {
    return promise.then((response) => {
        const ret = {
            feCategoryCode: 1,
            msg: '服务器网络异常，请稍后重试',
        };
        if (response && response.json && typeof response.json == 'function') {
            /**
             * 没有经过 JSON 解析的情况
             * 包括 content-type 不正确、网络状态码（500、401 等）错误
             * 这里做统一的弹窗
             */
            if (response.status === 401) {
                ret.msg = '用户认证失败，请重新登录';
            }

            if (response.status === 403) {
                ret.msg = '您没有权限';
            }

            actionToast(ret.msg);

            throw ret;
        } else {
            let resData = response;
            // 业务异常
            if (!validateBizStatus(url, resData)) {

                ret.msg = resData.msg || '服务器业务异常，请稍后重试';
                ret.data = resData.data;
                if (response.status === 401) {
                    ret.msg = '用户认证失败，请重新登录';
                }

                if (response.status === 403) {
                    ret.msg = '您没有权限';
                }
                // actionToast(ret.msg);
                throw ret;
            } else {
                // mock数据返回的是text-chain
                if (typeof resData !== 'object') {
                    resData = JSON.parse(resData);
                }
                return resData;
            }
        }
    })
        .catch((error) => {
            // console.error(error); // console.error 方法会导致错误的上报

            /**
             * 过滤掉前端已归类并弹窗运营的错误
             */
            if (error && error.feCategoryCode && error.msg) {
                throw error;
            }

            /**
             * 用户自身网络异常、代码逻辑错误
             */
            const ret = {
                feCategoryCode: 2,
                msg: '代码逻辑错误',
            };

            if (error && error.message && error.stack) {
                if (error.message === 'Failed to fetch') {
                    ret.msg = '网络连接异常，请检查您的网络';
                } else {
                    ret.msg = error.message;
                }
            }

            actionToast(ret.msg);

            throw ret;
        });
}

function actionToast(msg) {
    message.error(msg)
}

export {
    monitorFilter,
    parseResponseFilter,
};
