/* eslint-disable */
export default class Pipe {
    constructor() {
        this.flows = [];
    }

    use(func) {
        if (typeof func === 'function') {
            this.flows.push(func);
        }
    }


    run(...args) {
        let target = args[0];

        if (target && typeof target.then === 'function') {
            this.flows.forEach((func) => {
                const tmp = [].slice.call(args, 1);
                tmp.unshift(target);
                target = func.apply(null, tmp);
            });
            return target;
        } else {
            return null
        }
    }
}

