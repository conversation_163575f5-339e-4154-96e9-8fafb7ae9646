import { NewSignTargetStatus } from "../types";

// 电销新签工作台-跟进状态
export const FOLLOW_STATUS = [
    {
        label: '待分配',
        value: '-3,-2,-1,0',
    },
    {
        label: '待首次跟进',
        value: '1',
    },
    {
        label: '待二次跟进',
        value: '2',
    },
    {
        label: '待三次跟进',
        value: '3',
    },
    {
        label: '已闭环',
        value: '4,5',
    },
];

export const FOLLOW_STATUS_SG = [
    {
        label: '待分配',
        value: '-3,-2,-1,0',
    },
    {
        label: '待首次跟进',
        value: '1',
    },
    {
        label: '待二次跟进',
        value: '2',
    },
    {
        label: '待三次跟进',
        value: '3',
    },
    {
        label: '已闭环',
        value: '4,5',
    },
];

export const FOLLOW_STATUS_QY = [
    {
        "value": '1',
        "label": "待分配"
    },
    {
        "value": '2',
        "label": "已触达"
    },
    {
        "value": '3',
        "label": "对账中"
    },
    {
        "value": '4',
        "label": "开具中"
    },
    {
        "value": '5',
        "label": "已开具"
    },
    {
        "value": '6',
        "label": "已倒闭"
    }
];

// 电销新签工作台-来源渠道

export const SOURCE_STATUS = [
    {
        label: '待分配',
        value: '0',
    },
    {
        label: '待首次跟进',
        value: '1',
    },
    {
        label: '待二次跟进',
        value: '2',
    },
    {
        label: '待三次跟进',
        value: '3',
    },
    {
        label: '已闭环',
        value: '4,5',
    },
];


// 电销新签工作台-线索头部
export const HEAD_LABEL = [
           {
               label: '全部线索',
               countField: 'allCount',
               count: '0',
               status: '-1',
               children: [
                   {
                       label: '全部线索',
                       value: '0',
                   },
                   {
                       label: '付费推广池',
                       value: '1',
                   },
                   {
                       label: 'CRM池',
                       value: '2',
                   },
                   {
                       label: '商家体验组咨询',
                       value: '3',
                   },
                   {
                       label: '未生成商机池',
                       value: '4',
                   },
                   {
                       label: 'BD转介绍（未沟通意向）',
                       value: '5',
                   },
                   {
                       label: 'BD转介绍（已明确意向）',
                       value: '23',
                   },
                   {
                       label: '酒店',
                       value: '6',
                   },
                   {
                       label: 'AI外呼',
                       value: '7',
                   },
                   {
                       label: '商户转介绍（自己的其他店）',
                       value: '13',
                   },
                   {
                       label: '商户转介绍（其他人的店）',
                       value: '14',
                   },
                   {
                       label: '美食城店铺',
                       value: '15',
                   },
                   {
                       label: '坐席自拓展',
                       value: '16',
                   },
                   {
                       label: '单店暂不合作',
                       value: '8',
                   },
                   {
                       label: '商家服务咨询',
                       value: '9',
                   },
                   {
                       label: '团购',
                       value: '10',
                   },
                   {
                       label: '退出期商户',
                       value: '17',
                   },
                   {
                       label: '其他wdcID',
                       value: '18',
                   },
                   {
                       label: '其他电话',
                       value: '19',
                   },
                   {
                       label: '代运营推荐',
                       value: '20',
                   },
                   {
                       label: '商服工单',
                       value: '21',
                   },
                   {
                       label: '自入驻企微咨询',
                       value: '22',
                   },
               ],
           },
           {
               label: '待分配',
               status: '-3,-2,-1,0',
               countField: 'unAssignCount',
               count: '0',
           },
           {
               label: '待首次跟进',
               status: NewSignTargetStatus.WAIT_FIRST_FOLLOW,
               countField: 'firstFollowCount',
               count: '0',
           },
           {
               label: '待二次跟进',
               status: NewSignTargetStatus.WATI_SECOND_FOLLOW,
               countField: 'secondFollowCount',
               count: '0',
           },
           {
               label: '待三次跟进',
               status: NewSignTargetStatus.WATI_THIRD_FOLLOW,
               countField: 'thirdFollowCount',
               count: '0',
           },
           {
               label: '已闭环',
               status: `${NewSignTargetStatus.SUCCESS_CLOSED_LOOP},${NewSignTargetStatus.FAILURE_CLOSED_LOOP}`,
               countField: 'circleCount',
               count: '0',
           },
       ];


// 电销新签工作台-线索头部 闪购业务线
export const HEAD_LABEL_SG = [{
    label: '全部线索',
    countField: 'allCount',
    count: '0',
    status: '-1',
    children: [{
        label: '全部线索',
        value: '0',
    },
    {
        label: 'CRM池',
        value: '2',
    },
    {
        label: '线索投放',
        value: '11',
    },
    {
        label: '市场活动',
        value: '12',
    }],
}, {
    label: '待分配',
    status: '-3,-2,-1,0',
    countField: 'unAssignCount',
    count: '0',

}, {
    label: '待首次跟进',
    status: '1',
    countField: 'firstFollowCount',
    count: '0',
}, {
    label: '待二次跟进',
    status: '2',
    countField: 'secondFollowCount',
    count: '0',
}, {
    label: '待三次跟进',
    status: '3',
    countField: 'thirdFollowCount',
    count: '0',
}, {
    label: '已闭环',
    status: '4,5',
    countField: 'circleCount',
    count: '0',
}];

// 电销新签工作台-线索头部 企业业务线
export const HEAD_LABEL_QY = [{
    label: '全部线索',
    countField: 'allCount',
    count: '0',
    status: '-1',
    children: [],
}, {
    label: '待分配',
    status: '1',
    countField: 'unAssignCount',
    count: '0',

}, {
    label: '已触达',
    status: '2',
    countField: 'reachedCount',
    count: '0',
}, {
    label: '对账中',
    status: '3',
    countField: 'reconcilingCount',
    count: '0',
}, {
    label: '开具中',
    status: '4',
    countField: 'preparingCount',
    count: '0',
}, {
    label: '已开具',
    status: '5',
    countField: 'issuedCount',
    count: '0',
}, {
    label: '已倒闭',
    status: '6',
    countField: 'closedCount',
    count: '0',
}];

// 电销新签工作台-闭环类型
export const CLOSE_LOOP_TYPE = [
    {
        label: '成功闭环',
        value: 1,
    },
    {
        label: '失败闭环',
        value: 2,
    },
];


export const BizEnum = {
    WM: 1000008, // 外卖
    SG: 1000010, // 闪购
    QY: 555,// 企业
};

// 拜访方式
export const VisitType = [
    { label: '上门拜访', value: '上门拜访' },
    { label: '电话拜访', value: '电话拜访' },
    { label: '微信拜访', value: '微信拜访' },
]
export default {};

