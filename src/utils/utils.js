import moment from 'moment';

// eslint-disable-next-line
export const timeRender = time => {
    const timeStampLength = (
        `${time}`).length;
    if (timeStampLength === 10) {
        return moment(time * 1000)
            .format('YYYY-MM-DD HH:mm:ss');
    }
    if (timeStampLength === 13) {
        return moment(time)
            .format('YYYY-MM-DD HH:mm:ss');
    }
    return '-';
};


const padTime = time => String(time)
    .padStart(2, '0');

export const parseTime = (time) => {
    const secNum = parseInt(String(time / 1000), 10);
    const hours = Math.floor(secNum / 3600);
    const minutes = Math.floor((
        secNum - (
            hours * 3600)) / 60);
    const seconds = secNum - (
        hours * 3600) - (
            minutes * 60);
    return `${padTime(hours)}:${padTime(minutes)}:${padTime(seconds)}`;
};


// 跳转其他系统host
export const getHost = () => {
    // 活动配置 跳转链接
    let host = 'https://portal-wdc.waimai.sankuai.com/web/create/poi';

    if (
        window.location.href.indexOf('igate.waimai.test.sankuai.com/igate') > -1 || window.location.href.indexOf('wm-ocrm.waimai.test.sankuai.com') > -1
    ) {
        host = 'http://portal.wdc.waimai.test.sankuai.com/web/create/poi';
    } else if (
        window.location.href.indexOf('igate.waimai.dev.sankuai.com/igate') > -1 || window.location.href.indexOf('wm-ocrm.waimai.dev.sankuai.com') > -1
    ) {
        host = 'http://portal.wdc.waimai.dev.sankuai.com/web/create/poi';
    } else if (
        window.location.href.indexOf('igate.waimai.st.sankuai.com/igate') > -1 || window.location.href.indexOf('wm-ocrm.waimai.st.sankuai.com') > -1
    ) {
        host = 'http://portal.wdc.waimai.st.sankuai.com/web/create/poi';
    }

    return host;
};

export const getCluePath = () => {
    let path = '/page';
    if (window.location.host.indexOf('igate.waimai') > -1) {
        path = '/igate';
    }
    return path;
};

export const downloadImage = function (imgsrc, name) { // 下载图片地址和图片名
    const image = new Image();
    // 解决跨域 Canvas 污染问题
    image.setAttribute('crossOrigin', 'anonymous');
    image.onload = function () {
        const canvas = document.createElement('canvas');
        canvas.width = image.width;
        canvas.height = image.height;
        const context = canvas.getContext('2d');
        context.drawImage(image, 0, 0, image.width, image.height);
        const url = canvas.toDataURL('image/png'); // 得到图片的base64编码数据
        const a = document.createElement('a'); // 生成一个a元素
        const event = new MouseEvent('click'); // 创建一个单击事件
        a.download = name || 'photo'; // 设置图片名称
        a.href = url; // 将生成的URL设置为a.href属性
        a.dispatchEvent(event); // 触发a的单击事件
    };
    image.src = imgsrc;
};


export const authCreate = (auth) => {
    const controlMap = {
        ...auth,
        uiControl: {
            // 认领
            newsign_claim: auth.is_admin || auth.is_hq || auth.is_business,
            // 搜索
            newsign_search:
                auth.is_admin ||
                auth.is_hq ||
                auth.is_business ||
                auth.is_quality,
            // 分配
            newsign_assign: auth.is_admin || auth.is_hq,
            // 批量分配
            newsign_batch_assgin:
                auth.is_admin || auth.is_hq || auth.is_enterprise_admin,
            // 导入
            newsign_import:
                auth.is_admin || auth.is_hq || auth.is_enterprise_admin,
            // 导出
            newsign_export:
                auth.is_admin ||
                auth.is_hq ||
                auth.is_quality ||
                auth.is_enterprise_business,
            // 打标
            newsign_label: auth.is_admin || auth.is_hq || auth.is_business,
            // 建店
            newsign_buildPoi: auth.is_admin || auth.is_hq || auth.is_business,
            // 号码查看
            phone_see:
                auth.is_admin ||
                auth.is_hq ||
                auth.is_business ||
                auth.is_quality,
            // 新增/修改
            newsign_create: auth.is_admin || auth.is_hq || auth.is_business,
            // 变更认领人
            newsign_reassign: auth.is_hq,
            // 线索清理
            newsign_clean: auth.is_hq,
        },
    };


    return controlMap;
};

export const phoneEncrypt = (phone) => {
    phone = `${phone}`;
    const reg = /(\d{3})\d{4}(\d*)/; // 正则表达式
    return phone.replace(reg, '$1****$2');
};
