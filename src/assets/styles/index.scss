html {
  height: 100%;
}

body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>',
    'Oxygen', 'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans',
    'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.app-root {
  height: 100%;
}

.app {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  height: 100%;

  .app-body {
    text-align: center;
  }

  .app-intro {
    margin-top: 20px;
    color: #888;
  }

  .app-logo {
    width: 150px;
  }

  .app-title {
    color: #666;
  }
}

.app-footer {

  p,
  strong {
    font-size: 12px;
    color: #666;
    line-height: 1.5em;
    margin: 0 auto;
    text-align: left;
    padding: 0 40px;
  }

  strong {
    font-weight: 800;
  }
}

.page-content {
  height: 100%;
  margin: 0 0 0 222px;
  padding-top: 70px;
  display: -ms-flexbox;
  display: flex
}

.ant-modal-close-x {
  display: flex;
  align-items: center;
  justify-content: center;
}


.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
}