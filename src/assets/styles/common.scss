// margin padding
@for $i from 1 through 50 {
  .mt-#{$i} {
    margin-top: #{$i}px;
  }

  .mb-#{$i} {
    margin-bottom: #{$i}px;
  }

  .ml-#{$i} {
    margin-left: #{$i}px;
  }

  .mr-#{$i} {
    margin-right: #{$i}px;
  }

  .pt-#{$i} {
    padding-top: #{$i}px;
  }

  .pb-#{$i} {
    padding-bottom: #{$i}px;
  }

  .pl-#{$i} {
    padding-left: #{$i}px;
  }

  .pr-#{$i} {
    padding-right: #{$i}px;
  }
}

/*font-size*/
@for $i from 10 through 30 {
  .fs-#{$i} {
    font-size: #{$i}px;
  }
}

/*font-weight*/
$fontWeightArr: 200, 300, 400, 500, 600, 700;

@each $i in $fontWeightArr {
  .fw-#{$i} {
    font-weight:#{$i};
  }
}

/*flex*/
.flex {
  display: flex;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.flex-align {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flex-around {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
}

.flex-between {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}