$prefix: lead-detail;

// 去除antd bredcrumb的左padding
.ant-breadcrumb {
  ol {
    padding-inline-start: 0 !important;
  }
}

.#{$prefix} {
  &-wrapper {
    width: 100%;
    background-color: white;
    padding: 20px;
  }

  &-gray {
    background-color: #f2f2f2;
  }

  &-relative {
    position: relative;
  }

  &-no-padding {
    padding: 0;
  }

  &-flex {
    display: flex;
  }

  &-flex-column {
    flex-direction: column;
  }

  &-justify-center {
    justify-content: center;
  }

  &-align-center {
    align-items: center;
  }

  &-ml-sm {
    margin-left: 20px;
  }

  &-bold {
    font-weight: bold;
  }

  &-absolute {
    position: absolute !important;
    top: 0;
    right: 0;
  }

  &-ml-md {
    margin-left: 10px;
  }

  &-br-sm {
    border-radius: 5px;
  }

  &-mt-5px {
    margin-top: 5px;
  }

  &-mr-sm {
    margin-right: 10px;
  }

  &-pd-sm {
    padding: 10px;
  }

  &-mt-sm {
    margin-top: 10px;
  }

  &-mb-sm {
    margin-bottom: 10px;
  }

  &-tx-blue {
    color: #3C99D8;
  }
}