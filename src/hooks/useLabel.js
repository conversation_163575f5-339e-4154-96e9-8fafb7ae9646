import { useEffect, useState } from 'react';

import service from '../common/service';
import { apiMap } from '../common/service/apiMap';

// 获取最新标签
function useLabel(processCode, activeBizId) {
    const [label, setLabel] = useState([]);
    useEffect(() => {
        function trans(arr) {
            return arr.map((item) => {
                const obj = { value: item.labelId, label: item.comment };
                if (item.childLabel) {
                    obj.children = [{ value: -1, label: '全部' }].concat(trans(item.childLabel || []));
                }
                return obj;
            });
        }
        if (activeBizId) {
            service.get(apiMap.screenLabel.path, { processCode, bizId: activeBizId }).then((res) => {
                if (res.code === 0) {
                    setLabel([{ value: -1, label: '全部' }].concat(trans(res.data)));
                }
            });
        }
    }, [activeBizId]);
    return label;
}
export default useLabel;
