import {
    useEffect,
    useState,
} from 'react';

import service from '../common/service';
import { apiMap } from '../common/service/apiMap';

// 获取城市一级
function useCitysTopLevel() {
    const [city, setCity] = useState();
    useEffect(() => {
        service.get(apiMap.getCityInfo.path)
            .then((res) => {
                if (res.code === 0) {
                    setCity(res.data);
                }
            });
    }, []);
    return city;
}

export default useCitysTopLevel;
