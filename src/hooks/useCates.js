import { useEffect, useState } from 'react';

import service from '../common/service';
import { apiMap } from '../common/service/apiMap';

// 获取品类
function useCates(isAll = true) {
    const [cate, setCate] = useState([]);
    useEffect(() => {
        function trans(arr) {
            return arr.map((item) => {
                const obj = { value: item.id, label: item.name };
                if (!item.is_leaf && item.level < 3) {
                    if (isAll) {
                        obj.children = [{ value: -1, label: '全部' }].concat(trans(item.subcate));
                    } else {
                        obj.children = trans(item.subcate);
                    }
                }
                return obj;
            });
        }

        service.get(apiMap.getCates.path).then((res) => {
            if (res.code === 0) {
                if (isAll) {
                    setCate([{ value: -1, label: '全部' }].concat(trans(res.data)));
                } else {
                    setCate(trans(res.data));
                }
            }
        });
    }, []);
    return cate;
}
export default useCates;
