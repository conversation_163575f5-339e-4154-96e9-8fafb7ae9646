/* eslint-disable react/prop-types,import/extensions,import/no-unresolved */
import React from 'react';
import { DifferentItem } from '@mfe/diff-react';

import './index.scss';

const Info = ({
    title,
    data,
    cols,
    className,
}) => {
    const content = (col) => {
        if (!data) {
            return col.defaultValue || '';
        }
        if (col.render) {
            return col.render(data[col.prop], data);
        }
        return data[col.prop] || col.defaultValue;
    };

    return (
        <dl className={`info ${className}`}>
            {title && <dd className="tx-title" key="title">{title}</dd>}
            {cols &&
                cols.map((col) => {
                    const {
                        label,
                        prop,
                        showLogic,
                    } = col;
                    // 根据值控制是否展示
                    if (showLogic && !showLogic(data[prop], data)) {
                        return null;
                    }
                    return (
                        <DifferentItem diffId={col.prop}>
                            <dd className="tx-text flex" key={prop}>
                                <span style={{ color: '#222' }}>{label}：</span>{content(col)}
                            </dd>
                        </DifferentItem>);
                })
            }
        </dl>
    );
};

export default Info;
