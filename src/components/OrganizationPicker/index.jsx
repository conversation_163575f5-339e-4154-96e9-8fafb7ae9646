import React from 'react';
import { Organization } from './Organization';
import { Picker } from './Picker';
import { variables, styles } from './styles';

export class OrganizationPicker extends React.Component {
  static defaultProps = {
      className: '',
      style: {},
      contentContainerStyle: {},
      configs: undefined,
      checkType: 'checkbox',
      placeholder: '请选择组织结构',
      inputKey: '',
      value: [],
      onChange: null,
  };

  constructor(props) {
      super(props);

      this.state = {
          pickerString: '',
          panelShowed: false,
      };

      if (props.value && props.value.length > 0) {
          props.onBind && props.onBind(props.value);
      }
  }

  handleInit = (options) => {
      this.setState({
          pickerString: this.resetPickerString(this.props.value, options),
      });
  };

  handleWillReceiveProps = (value, options) => {
      this.setState({
          pickerString: this.resetPickerString(value, options),
      });
  };

  handleChange = (value) => {
      const pickerString = this.resetPickerString(
          value,
          this._organization.state.options,
      );
      this.setState({
          pickerString,
      });
      this.props.onChange && this.props.onChange(value);
  };

  resetPickerString(value, options) {
      value = value || [];
      options = options || [];
      let pickerString = '';

      if (!options.length || !value.length) {
          return pickerString;
      }

      if (value.length === 1) {
          options.some((item) => {
              if (item.id === value[0]) {
                  pickerString = item.name;
                  return true;
              }
              return false;
          });
      }

      if (value.length > 1) {
          pickerString = `您已选择${value.length}个`;
      }

      return pickerString;
  }

  handleClear = () => {
      const { disabled } = this.props;
      if (disabled) return;
      this.handleChange([]);
  };

  handleCancel = () => {
      this.setState({
          panelShowed: false,
      });
  };

  handleConfirm = () => {
      this.setState({
          panelShowed: false,
      });
  };

  render() {
      const {
          value,
          onChange,
          className,
          style,
          contentContainerStyle,
          placeholder,
          checkType,
          configs,
          disabled,
          isKaOrCka,
          inputKey,
          mode,
      } = this.props;
      const { pickerString, panelShowed } = this.state;
      return (
          <Picker
              className={className}
              disabled={disabled}
              style={style}
              contentContainerStyle={{
                  ...styles.organizationWrapper,
                  ...contentContainerStyle,
              }}
              placeholder={placeholder}
              inputKey={inputKey}
              value={pickerString}
              showed={panelShowed}
              onClear={this.handleClear}
              onToggle={(panelShowed) => {
                  this.setState({
                      panelShowed,
                  });
              }}
          >
              <Organization
                  ref={(c) => {
                      this._organization = c;
                  }}
                  inputKey={inputKey}
                  checkType={checkType}
                  configs={configs}
                  onInit={this.handleInit}
                  onWillReceiveProps={this.handleWillReceiveProps}
                  value={value}
                  onCancel={this.handleCancel}
                  onConfirm={this.handleConfirm}
                  onChange={this.handleChange}
                  isKaOrCka={isKaOrCka}
                  mode={mode}
              />
          </Picker>
      );
  }
}
