import React from 'react';
import { <PERSON>r, Tree } from './Cascader';
import getPrefix from '../../common/service/getPrefix';
import axios from 'axios';
import { variables, organStyles as styles } from './styles';

function debounce(fn, delay) {
    let timeout = null;
    return function () {
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            fn.apply(this, arguments);
        }, delay);
    };
}

const fieldKeys = {
    idKey: 'id',
    pIdKey: 'pId',
    childrenKey: 'childrenList',
    labelKey: 'name',
    activeKey: 'active',
    checkedKey: 'checked',
    disabledKey: 'disabled',
};

const DEFAULT_CONFIGS = {
    backtrackOrgType: 20,
};

export class Organization extends React.Component {
  static defaultProps = {
      className: '',
      style: {},
      apis: {
          getByPid: '/uicomponent/api/orgs/getByPid',
          search: '/uicomponent/api/orgs/search',
          getUserSources: '/uicomponent/getUserSources',
      },
      checkType: 'checkbox',
      configs: {},
      onInit: null,
      onWillReceiveProps: null,
      onChange: null,
      onConfirm: null,
      onCancel: null,
      onBind: null,
      inputKey: '',
  };

  constructor(props) {
      super(props);
      this.state = {
          options: [],
          queryOptions: [],
          value: [],
          showCascader: true,
          querystring: '',
          loadingData: false,
          loading: false,
      };
      this.resetStateNum = 0; // 用于精确控制异步获取的数据，防止因延迟，导致前面数据覆盖后面数据
      this.fetchDataDebounce = debounce(this.fetchDataAndSetState, 500);
  }

  init(props, options, resetStateNum) {
      const value = props.value || [];

      options = options || [];
      let optionsIncludeValue = false;

      if (!options.length) {
          optionsIncludeValue = false;
      }

      if (options.length && !value.length) {
          optionsIncludeValue = true;
      }

      if (options.length && value.length) {
          optionsIncludeValue = value.every(valueItem => options.some(optionItem => optionItem[fieldKeys.idKey] === valueItem));
      }

      let p;
      if (!optionsIncludeValue) {
          document.getElementById(`organizationPicker__input__${props.inputKey}`).setAttribute('disabled', true);
          document.getElementById(`organizationPicker__input__${props.inputKey}`).setAttribute('placeholder', '加载组织架构中，请稍后');
          p = this.fetchData(this.props.apis.getByPid, {
              orgIds: value.length ? value.join('_') : undefined,
          }).then((ret) => {
              document.getElementById(`organizationPicker__input__${props.inputKey}`).disabled = '';
              document.getElementById(`organizationPicker__input__${props.inputKey}`).setAttribute('placeholder', '请选择组织架构');

              const fetchedOptions = ret || [];
              return this.mergeOptions(options, fetchedOptions, fieldKeys);
          });
      } else {
          p = Promise.resolve(options);
      }

      return p.then((options) => {
          const tmpValue = value.filter((valueItem) => {
              const valueFinded = options.some(optionItem => optionItem[fieldKeys.idKey] === valueItem);
              if (!valueFinded) {
                  console.log(`值 ${valueItem} 在数据集合中不存在！`);
              }
              return valueFinded;
          });

          return {
              value: tmpValue,
              options,
              resetStateNum,
          };
      });
  }

  componentDidMount() {
      this.init(this.props, [], ++this.resetStateNum).then((ret) => {
          if (ret.resetStateNum !== this.resetStateNum) {
              return;
          }
          this.setState(
              {
                  value: ret.value,
                  options: ret.options || [],
              },
              () => {
                  this.props.onInit && this.props.onInit(this.state.options);
              },
          );
      });
  }

  componentWillReceiveProps(nextProps) {
      if (nextProps.value !== this.props.value) {
          this.init(nextProps, this.state.options, ++this.resetStateNum).then((ret) => {
              if (ret.resetStateNum !== this.resetStateNum) {
                  return;
              }
              this.setState(
                  {
                      value: ret.value,
                      options: ret.options,
                  },
                  () => {
                      this.props.onWillReceiveProps &&
                this.props.onWillReceiveProps(
                    nextProps.value,
                    this.state.options,
                );
                  },
              );
          });
      }
  }

  mergeOptions(options, addedOptions, fieldKeys) {
      const newOptions = [...options];

      addedOptions.forEach((addedItem) => {
          const existed = options.some(optionItem => optionItem[fieldKeys.idKey] === addedItem[fieldKeys.idKey]);

          if (!existed) {
              newOptions.push(addedItem);
          }
      });

      return newOptions;
  }

  fetchSources() {
      // TODO mock;
      this.sources = [];
      // this.sources = [1, 4, 5, 6];
      if (this.sources && this.sources.length) {
          return Promise.resolve(this.sources);
      } else {
          return axios
              .get(`${getPrefix(this.props.apis.getUserSources)}${this.props.apis.getUserSources}`)
              .then((res) => {
                  res = res.data;
                  if (res && res.code === 0) {
                      this.sources = res.data;
                      return this.sources;
                  }
              })
              .catch(() => {});
      }
  }

  getListOrgTypeArr = (data) => {
      const result =
      data &&
      data.map(item => item.orgType);
      return result;
  };

  isOnlyShowKa = (data) => {
      const { isKaOrCka } = this.props;
      return isKaOrCka && this.getListOrgTypeArr(data).indexOf(26) > -1;
  };

  fetchData(api, params) {
      const { configs, isKaOrCka } = this.props;
      api = `${getPrefix(api)}${api}`;
      return this.fetchSources()
          .then((sources) => {
              sources = sources || '';
              return axios
                  .get(api, {
                      params: {
                          ...DEFAULT_CONFIGS,
                          ...configs,
                          ...params,
                          sources: sources.join('_'),
                      },
                  })
                  .then((res) => {
                      res = res.data;
                      if (res && res.code === 0) {
                          let list = res.data.list;
                          if (this.props.isKaOrCka && this.isOnlyShowKa(list)) {
                              list = list.filter(item => item.orgType === 26);
                          }
                          return this.modOptionStructure(list);
                      } else {
                          return [];
                      }
                  });
          })
          .catch((err) => {
              // Promise.reject(err);
          });
  }

  handleClick = (item) => {
      if (this.isLeafNode(item)) {
          return;
      }

      // if (item[fieldKeys.childrenKey] && item[fieldKeys.childrenKey].length) {
      //     return;
      // }
      if (this.state.loadingData) {
          return;
      }

      this.setState(
          {
              loadingData: true,
          },
          () => {
              this.addChildren(this.state.options, item)
                  .then((tmpOptions) => {
                      this.setState({
                          options: tmpOptions,
                          loadingData: false,
                      });
                  })
                  .catch((e) => {
                      console.log(e);
                      this.setState({
                          loadingData: false,
                      });
                  });
          },
      );
  };

  handleChange = (value) => {
      this.setState({
          value,
      });
      // this.props.onChange && this.props.onChange(value);
  };

  addChildren(options, item) {
      return this.fetchData(this.props.apis.getByPid, {
          parentId: item[fieldKeys.idKey],
      }).then((ret) => {
          ret = ret || [];
          const tmpOptions = [
              ...options,
              ...ret
                  .filter(retItem => !options.some(optionItem => optionItem[fieldKeys.idKey] === retItem[fieldKeys.idKey]))
                  .map(retItem => ({
                      ...retItem,
                      [fieldKeys.pIdKey]: item[fieldKeys.idKey],
                  })),
          ];

          return tmpOptions;
      });
  }

  isLeafNode(item) {
      return !!item.isLeaf;
  }

  handleClear = () => {
      this.handleChange([]);
  };

  handleConfirm = () => {
      this.props.onConfirm && this.props.onConfirm();
      this.props.onChange && this.props.onChange(this.state.value);
  };

  handleCancel = () => {
      this.props.onCancel && this.props.onCancel();
  };

  getLabels(tree, value) {
      const { checkType } = this.props;
      value = value || [];
      value.forEach((valueItem) => {
          const target = tree.filter(treeItem => treeItem[fieldKeys.idKey] === valueItem)[0];
          if (!target) {
              return;
          }
          tree = Cascader.resetChecked(tree, target, checkType, true, fieldKeys);
      });

      return Cascader.getLabels(
          Cascader.getCheckedInfo(tree, checkType, fieldKeys).checkedResult,
          fieldKeys,
      );
  }

  handleChangeInput = (querystring) => {
      this.setState({
          querystring,
      });
      this.fetchDataDebounce(this.props.apis.search, {
          keyword: querystring,
      });
  };

  fetchDataAndSetState = (api, params) => {
      this.fetchData(api, params).then((ret) => {
          const fetchedOptions = ret || [];
          const newOptions = this.mergeOptions(
              this.state.options,
              fetchedOptions,
              fieldKeys,
          );

          this.setState({
              queryOptions: fetchedOptions,
              options: newOptions,
              hoverItemIndex: null,
          });
      });
  };

  modOptionStructure(options) {
      const tree = new Tree({
          type: 'nested',
          ...fieldKeys,
          data: options,
      })
          .getData()
          .map(item => ({
              ...item,
              [fieldKeys.disabledKey]: !item.editable,
          }));

      return tree;
  }

  getBreadcrumb(options) {
      if (!options || !options.length) {
          return [];
      }
      return Cascader.getBreadcrumb(options, fieldKeys);
  }

  handleClickQueryResult = (item, checked) => {
      const { checkType } = this.props;
      const options = Cascader.resetChecked(
          this._cascader.state.tree,
          item,
          checkType,
          !checked,
          fieldKeys,
      );
      const { checkedValue } = Cascader.getCheckedInfo(
          options,
          checkType,
          fieldKeys,
      );

      this.handleChange(checkedValue);
  };

  renderQueryResult(queryBreadcrumb) {
      const { queryOptions, value } = this.state;

      return (
          <ul style={{ display: 'inline-block', minWidth: '100%' }}>
              {queryBreadcrumb.map((breadcrumbItem, breadcrumbIndex) => {
                  const target = breadcrumbItem[breadcrumbItem.length - 1];

                  const checked = value.some(valueItem => breadcrumbItem.some(item => item[fieldKeys.idKey] === valueItem));

                  return (
                      <li
                          key={breadcrumbIndex}
                          style={{
                              ...styles.resultItem,
                              backgroundColor:
                  this.state.hoverItemIndex === breadcrumbIndex
                      ? variables.hoverBackgroundColor
                      : checked
                          ? variables.checkedBackgroundColor
                          : 'transparent',
                          }}
                          onMouseEnter={() => {
                              this.setState({
                                  hoverItemIndex: breadcrumbIndex,
                              });
                          }}
                          onMouseLeave={() => {
                              this.setState({
                                  hoverItemIndex: null,
                              });
                          }}
                          onClick={this.handleClickQueryResult.bind(this, target, checked)}
                      >
                          <input
                              style={{ marginRight: '3px' }}
                              type={this.props.checkType}
                              checked={checked}
                              readOnly
                              disabled={target[fieldKeys.disabledKey]}
                          />
                          {breadcrumbItem.map((item, index) => (
                              <span key={index}>
                                  {item[fieldKeys.labelKey]}
                                  {index < breadcrumbItem.length - 1 ? '>' : ''}
                              </span>
                          ))}
                      </li>
                  );
              })}
          </ul>
      );
  }

  render() {
      const {
          checkType, style, className, mode,
      } = this.props;
      const {
          options, value, showCascader, querystring, queryOptions,
      } =
      this.state;
      const labels = this.getLabels(options, value);
      const queryBreadcrumb = this.getBreadcrumb(queryOptions);

      return (
          <div
              className={className}
              style={{
                  ...styles.container,
                  ...style,
              }}
          >
              <div style={styles.searchPanel}>
                  <input
                      placeholder="搜索内容至少两个字符"
                      style={styles.input}
                      type="text"
                      value={querystring}
                      onChange={(e) => {
                          this.handleChangeInput(e.target.value);
                      }}
                      onFocus={() => {
                          this.setState({
                              showCascader: false,
                          });
                      }}
                  />

                  <div
                      style={{
                          display: !showCascader ? 'block' : 'none',
                      }}
                  >
                      <div style={styles.statusBar}>
                          <span>匹配到 {queryBreadcrumb.length} 个类目</span>

                          <button
                              className="roo-btn roo-btn-default"
                              type="button"
                              onClick={() => {
                                  this.setState({
                                      showCascader: true,
                                  });
                              }}
                          >
                返回选择页
                          </button>
                      </div>

                      <div style={styles.searchResult}>
                          {this.renderQueryResult(queryBreadcrumb)}
                      </div>
                  </div>
              </div>

              <div
                  style={{
                      ...styles.cascaderPanel,
                      display: showCascader ? 'block' : 'none',
                  }}
              >
                  <Cascader
                      ref={(c) => {
                          this._cascader = c;
                      }}
                      {...this.props}
                      options={options}
                      value={value}
                      optionStructureType="flattened"
                      fieldKeys={fieldKeys}
                      isLeafNode={this.isLeafNode}
                      checkType={checkType}
                      onClick={this.handleClick}
                      onChange={this.handleChange}
                  />
              </div>

              <div style={styles.infoPanel}>
                  <div style={styles.infoPanelMain}>
                      <h3 style={styles.title}>
              当前已经选择
                          <span>{labels.length}</span>
              个节点
                      </h3>
                      <ul style={styles.labelPanel}>
                          {labels && labels.length
                              ? labels.map((item, index) => (
                                  <li key={index} style={styles.labelItem}>
                                      {item}
                                  </li>
                              ))
                              : null}
                      </ul>
                  </div>
                  <div style={styles.infoPanelOperator}>
                      {labels && labels.length && mode !== 'readonly' ? (
                          <button
                              className="roo-btn roo-btn-default"
                              type="button"
                              onClick={this.handleClear}
                          >
                清空
                          </button>
                      ) : null}
                      {' '}
                      {
                          mode !== 'readonly' ? (<button
                              className="roo-btn roo-btn-primary"
                              type="button"
                              onClick={this.handleConfirm}
                          >
                确定
                          </button>) : null
                      }
                      {' '}
                      <button
                          className="roo-btn roo-btn-default"
                          type="button"
                          onClick={this.handleCancel}
                      >
              取消
                      </button>
                  </div>
              </div>
          </div>
      );
  }
}
