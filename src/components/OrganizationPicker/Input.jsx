import React from 'react';
import { variables, inputStyles } from './styles';

export class Input extends React.Component {
  static defaultProps = {
      className: '',
      style: {},
      value: '',
      clearType: 'focus', // 'none' | 'static' | 'focus' | 'hover'(TODO)
      onChange: null,
  };

  constructor(props) {
      super(props);

      this.state = {
          inputFocused: false,
          inputFocusedDelay: false,
      };
      this.inputFocusedDelayId = null;
  }

  handleChange = (value) => {
      this.props.onChange && this.props.onChange(value);
  };

  handleFocus = (e) => {
      clearTimeout(this.inputFocusedDelayId);
      this.setState({
          inputFocused: true,
          inputFocusedDelay: true,
      });

      this.props.onFocus && this.props.onFocus(e);
  };

  handleBlur = (e) => {
      this.setState({
          inputFocused: false,
      });

      this.props.onBlur && this.props.onBlur(e);

      clearTimeout(this.inputFocusedDelayId);
      this.inputFocusedDelayId = setTimeout(() => {
          this.setState({
              inputFocusedDelay: false,
          });
      }, 500);
  };

  handleClear = () => {
      this.handleChange('');
  };

  toShowClear(clearType, value) {
      if (clearType === 'none') {
          return false;
      }

      if (clearType === 'static') {
          return !!value;
      }

      if (clearType === 'focus') {
          const { inputFocusedDelay } = this.state;
          return clearType && value && inputFocusedDelay;
      }
      return false;
  }

  render() {
      const {
          className, style, value, clearType, inputKey,
      } = this.props;
      const { inputFocused } = this.state;

      const showClear = this.toShowClear(clearType, value);

      const inputProps = {
          ...this.props,
      };

      delete inputProps.clearType;

      return (
          <div
              style={{
                  ...style,
                  ...inputStyles.container,
              }}
          >
              <input
                  {...inputProps}
                  style={{
                      ...inputStyles.input,
                      borderColor: this.state.inputFocused
                          ? variables.mtdBorderColorDark
                          : variables.mtdBorderColor,
                      paddingRight: showClear ? '30px' : variables.mtdHSpacingL,
                  }}
                  id={`organizationPicker__input__${inputKey}`}
                  type="text"
                  onChange={(e) => {
                      this.handleChange(e.target.value);
                  }}
                  onFocus={this.handleFocus}
                  onBlur={this.handleBlur}
              />
              {showClear ? (
                  <div style={inputStyles.clearIconWrapper} onClick={this.handleClear}>
                      <i
                          className="roo-icon roo-icon-times-circle"
                          style={{ color: variables.mtdGrayLightest, fontSize: 14 }}
                      />
                  </div>
              ) : null}
          </div>
      );
  }
}
// <Icon type='times-circle' size={14} />
