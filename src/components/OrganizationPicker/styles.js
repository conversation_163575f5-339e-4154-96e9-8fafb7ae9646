// 全局/品牌色
import commonVariables from './commonVariables';

export const variables = {
  ...commonVariables,

  paddingVertical: commonVariables.mtdVSpacingL,
  paddingHorizontal: commonVariables.mtdHSpacingXL,
  fontColor: commonVariables.mtdGrayDark,
  borderColor: commonVariables.mtdBorderColor,
  checkedBackgroundColor: commonVariables.mtdFillGrayLighter,
  hoverBackgroundColor: commonVariables.mtdFillGray,

  fontSize: commonVariables.mtdFontSizeM, // Cascader组件使用
  activeFontColor: commonVariables.mtdGrayBase, // Cascader组件使用
  backgroundColor: 'transparent', //
  activeBackgroundColor: commonVariables.mtdFillGray, // Cascader组件使用
  height: '200px', // Cascader组件使用
  itemMinWidth: '150px', // Cascader组件使用

  inputHeight: '36px', // Input组件使用
};

export const styles = {
  container: {},

  organizationWrapper: {
    width: '800px',
    marginTop: '2px',
    padding: `${variables.mtdVSpacingL} ${variables.mtdHSpacingXL}`,
    backgroundColor: variables.mtdFillGrayLighter,
    border: `1px solid ${variables.mtdBorderColor}`,
  },
};

export const pickerStyles = {
  container: {
    position: 'relative',
  },
  panel: {
    position: 'absolute',
    top: '100%',
    minWidth: '100%',
  },
};

export const inputStyles = {
  container: {
    position: 'relative',
  },
  input: {
    width: '100%',
    height: variables.inputHeight,
    padding: `${variables.mtdVSpacingS} ${variables.mtdHSpacingL}`,
    border: `1px solid ${variables.mtdBorderColor}`,
    borderRadius: variables.mtdRadiusXS,
    fontSize: variables.mtdFontSizeM,
    color: variables.mtdGrayDark,
    backgroundColor: '#fff',
    transition: 'border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out',
  },

  clearIconWrapper: {
    position: 'absolute',
    top: '0',
    right: '0',
    display: 'flex',
    height: variables.inputHeight,
    paddingRight: variables.mtdHSpacingL,
    alignItems: 'center',
    cursor: 'pointer',
  },
};

export const organStyles = {
  container: {
    backgroundColor: 'transparent',
    color: variables.fontColor,
  },
  searchPanel: {
    width: '500px',
  },

  input: {
    width: '100%',
    marginBottom: '10px',
    padding: '7px 10px',
    border: `1px solid ${variables.borderColor}`,
  },

  statusBar: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '0 0 10px',
  },

  searchResult: {
    height: '200px',
    border: `1px solid ${variables.borderColor}`,
    backgroundColor: '#fff',
    overflow: 'scroll',
  },

  resultItem: {
    display: 'block',
    padding: '7px 10px',
    whiteSpace: 'nowrap',
    cursor: 'pointer',
  },

  cascaderPanel: {
    backgroundColor: '#fff',
    border: `1px solid ${variables.borderColor}`,
    overflowX: 'scroll',
  },

  infoPanel: {
    display: 'flex',
    justifyContent: 'space-between',
    paddingTop: variables.paddingVertical,
  },

  infoPanelMain: {
    flex: 1,
    paddingRight: variables.paddingHorizontal,
    overflowX: 'hidden',
  },

  labelPanel: {
    overflowX: 'scroll',
    minHeight: '50px',
    maxHeight: '150px',
  },
  labelItem: {
    padding: '5px 0',
    whiteSpace: 'nowrap',
  },

  infoPanelOperator: {
    minWidth: '50px',
  },

  title: {
    margin: '0',
    fontSize: '16px',
    color: variables.mtdGrayBase,
    fontWeight: 'bold',
  },
};

export const casStyles = {
  container: {
    backgroundColor: 'transparent',
    color: variables.fontColor,
    whiteSpace: 'nowrap',
    lineHeight: variables.mtdLineHeight,
  },

  menuItem: {
    display: 'inline-block',
    height: variables.height,
    overflow: 'scroll',
  },

  item: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: `${variables.paddingVertical} ${variables.paddingHorizontal}`,
  },

  itemText: {
    display: 'flex',
    alignItems: 'center',
    fontSize: variables.fontSize,
  },

  menuItemActive: {
    backgroundColor: variables.activeBackgroundColor,
  },

  activeIcon: {
    width: 5,
    height: 8,
    position: 'absolute',
    right: 0,
  },
};
