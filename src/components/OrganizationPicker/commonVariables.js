// 全局/品牌色
const mtdBrandColors = {
  mtdBrandPrimaryDark: '#009acd',
  mtdBrandPrimary: '#00abe4',
  mtdBrandPrimaryLight: '#4cc4ec',
  mtdBrandPrimaryLighter: '#94d9ef',
  mtdBrandPrimaryLightest: '#cceefa',
  mtdBrandPrimaryLightest2: '#e5f6fc',

  mtdBrandSuccess: '#99d465',
  mtdBrandWarning: '#f8c833',
  mtdBrandDanger: '#ff5a5a',
  mtdBrandInfo: '#17a2b8',
};
// 灰度
const mtdGrayColors = {
  mtdGrayBase: '#2a2a2a',
  mtdGrayDarker: '#4a4a4a',
  mtdGrayDark: '#585A6E',
  mtdGray: '#8a8a8a',
  mtdGrayLight: '#a0a0a0',
  mtdGrayLighter: '#babccc',
  mtdGrayLightest: '#e0e0e0',
};
// 背景色
const mtdFillColors = {
  mtdFillBase: '#ffffff',
  mtdFillGray: '#f3f3f3',
  mtdFillGrayLight: '#F5F5F5',
  mtdFillGrayLighter: '#F7F8FA',
};
// 字体尺寸
const mtdFontSize = {
  mtdFontSizeXS: '10px',
  mtdFontSizeS: '12px',
  mtdFontSizeM: '14px',
  mtdFontSizeL: '16px',
  mtdFontSizeXL: '18px',
  mtdFontSizeXXL: '20px',
  mtdFontSizeXXXL: '22px',
  mtdFontSizeXXXXL: '24px',
  mtdFontSizeXXXXXL: '28px',
};

const mtdSpacing = {
  // 水平间距
  mtdHSpacingS: '5px',
  mtdHSpacingM: '8px',
  mtdHSpacingL: '10px',
  mtdHSpacingXL: '15px',
  mtdHSpacingXXL: '20px',

  // 垂直间距
  mtdVSpacingXS: '3px',
  mtdVSpacingS: '5px',
  mtdVSpacingM: '8px',
  mtdVSpacingL: '10px',
  mtdVSpacingXL: '12px',
  mtdVSpacingXXL: '15px',
  mtdVSpacingXXXL: '18px',
  mtdVSpacingXXXXL: '20px',
};

// 圆角
const mtdRadius = {
  mtdRadiusXS: '2px',
  mtdRadiusS: '4px',
  mtdRadiusM: '6px',
  mtdRadiusL: '8px',
};

const mtdBorder = {
  mtdBorderWidth: '1px',
  mtdBorderColor: '#E9EAF2',
  mtdBorderColorDark: '#A2A4B3',
};

const mtdLineHeight = 1.2;

const variables = {
  ...mtdBrandColors,
  ...mtdGrayColors,
  ...mtdFillColors,
  ...mtdFontSize,
  ...mtdSpacing,
  ...mtdRadius,
  ...mtdBorder,
  mtdLineHeight,
};

export default variables;
