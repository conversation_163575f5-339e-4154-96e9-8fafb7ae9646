import React from 'react';
import ReactDOM from 'react-dom';
import { pickerStyles } from './styles';
import { Input } from './Input';

export class Picker extends React.Component {
  static defaultProps = {
      className: '',
      style: {},
      contentContainerStyle: {}, // 弹出面板的样式
      value: '',
      placeholder: '请选择',
      onClear: null,
      onFocus: null,
      onToggle: null,
  };

  constructor(props) {
      super(props);

      this.state = {
          inputFocused: false,
      };
  }

  findDocumentNode(dom) {
      if (dom) {
          if (document.isEqualNode(dom)) {
              return dom;
          } else {
              return this.findDocumentNode(dom.parentNode);
          }
      } else {
          return null;
      }
  }

  clickEventListener = (e) => {
      const target = e.target;
      if (this._dom) {
          if (this._dom.contains(target)) {
              return;
          } else {
              const documentNode = this.findDocumentNode(e.target);
              if (!documentNode) {
                  return;
              }
          }
      }
      this.handleToggle(false);
  };

  keydownEventListener = (e) => {
      if (e.which === 27) {
          if (this._dom) {
              const input = this._dom.querySelectorAll('input');
              input[0].blur();
          }
          this.handleToggle(false);
      }
  };

  handleToggle(showed) {
      this.props.onToggle && this.props.onToggle(showed);
  }

  componentDidMount() {
      document.addEventListener('click', this.clickEventListener);
      document.addEventListener('keydown', this.keydownEventListener);
  }

  componentWillUnmount() {
      document.removeEventListener('click', this.clickEventListener);
      document.removeEventListener('keydown', this.keydownEventListener);
  }

  handleChange = (value) => {
      this.props.onClear && this.props.onClear();
  };

  render() {
      const {
          value,
          className,
          style,
          contentContainerStyle,
          children,
          placeholder,
          showed,
          disabled,
          clearType,
          inputKey,
      } = this.props;
      const { inputFocused } = this.state;

      return (
          <div
              ref={(c) => {
                  this._dom = c;
              }}
              className={className}
              style={{
                  ...pickerStyles.container,
                  ...style,
              }}
          >
              <Input
                  ref={(c) => {
                      this._input = c;
                  }}
                  disabled={disabled}
                  clearType={clearType || 'static'}
                  readOnly
                  inputKey={inputKey}
                  placeholder={placeholder}
                  value={value}
                  onFocus={() => {
                      if (disabled) return;
                      this.handleToggle(true);
                      this.props.onFocus && this.props.onFocus();
                  }}
                  onChange={this.handleChange}
              />
              <div
                  ref={(c) => {
                      this._panel = c;
                  }}
                  style={{
                      ...pickerStyles.panel,
                      display: showed ? 'block' : 'none',
                      ...contentContainerStyle,
                  }}
              >
                  {children}
              </div>
          </div>
      );
  }
}
