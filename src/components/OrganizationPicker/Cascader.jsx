import React from 'react';
import CheckBox from '@roo/roo/CheckBox';
import Radio from '@roo/roo/Radio';
import { variables, casStyles as styles } from './styles';
import Tree from './Tree';
export { Tree };

export class Cascader extends React.Component {
  static defaultProps = {
    checkType: 'checkbox', // 'radoo'
    optionStructureType: 'nested', // 'flattened'
    options: [],
    value: [],
    fieldKeys: {},
    onChange: null,
    onClick: null,
    renderItem: null,
    renderIcon: null,
    isLeafNode: null,
  };

  static getCheckedInfo(tree, checkType, fieldKeys) {
    const checkedValue = [];
    const checkedResult = [];

    if (checkType === 'checkbox') {
      tree.forEach((treeItem, treeIndex) => {
        if (treeItem[fieldKeys.checkedKey]) {
          const target = recursiveCheckbox(treeItem, treeIndex);
          const value = target[fieldKeys.idKey];
          if (checkedValue.indexOf(value) === -1) {
            checkedValue.push(value);
          }
        }
      });
    }

    if (checkType === 'radio') {
      tree.some((treeItem) => {
        if (treeItem[fieldKeys.checkedKey]) {
          checkedValue.push(treeItem[fieldKeys.idKey]);
          return true;
        }
        return false;
      });
    }

    checkedValue.forEach((valueItem, valueIndex) => {
      const target = tree.filter((treeItem) => {
        return treeItem[fieldKeys.idKey] === valueItem;
      })[0];
      const ancestors = Cascader.recursiveAncestors(tree, target, fieldKeys);
      ancestors.push(target);

      checkedResult[valueIndex] = ancestors;
    });

    return {
      checkedValue,
      checkedResult,
    };

    function recursiveCheckbox(item) {
      const parentItem = tree.filter((treeItem) => {
        return treeItem[fieldKeys.idKey] === item[fieldKeys.pIdKey];
      })[0];

      if (!parentItem) {
        return item;
      }

      if (parentItem && !parentItem[fieldKeys.checkedKey]) {
        return item;
      }

      return recursiveCheckbox(parentItem);
    }
  }

  static recursiveAncestors(tree, item, fieldKeys, ret) {
    ret = ret || [];
    const parentItem = tree.filter((treeItem) => {
      return treeItem[fieldKeys.idKey] === item[fieldKeys.pIdKey];
    })[0];

    if (parentItem) {
      ret = ret.concat();
      ret.unshift(parentItem);
      return Cascader.recursiveAncestors(tree, parentItem, fieldKeys, ret);
    } else {
      return ret;
    }
  }

  static getLabels(checkedResult, fieldKeys) {
    const labels = [];

    checkedResult.forEach((list, index) => {
      labels[index] = labels[index] || '';
      list.forEach((item) => {
        labels[index] = labels[index] + '>' + item[fieldKeys.labelKey];
      });

      labels[index] = labels[index].slice(1);
    });
    return labels;
  }

  static getBreadcrumb(tree, fieldKeys) {
    const ret = [];

    const leafItems = tree.filter((treeItem) => {
      const children = treeItem[fieldKeys.childrenKey];
      return !children || !children.length;
    });

    leafItems.forEach((item, index) => {
      ret[index] = [item];
      recursive(item, index);
    });

    return ret;

    function recursive(item, index) {
      tree.some((treeItem) => {
        if (treeItem[fieldKeys.idKey] === item[fieldKeys.pIdKey]) {
          ret[index] = ret[index] || [];
          ret[index].unshift(treeItem);
          recursive(treeItem, index);
          return true;
        }
        return false;
      });
    }
  }

  static resetChecked(tree, item, checkType, checked, fieldKeys) {
    let tmpTree = [...tree];

    if (checkType === 'checkbox') {
      tmpTree = resetCheckbox(tmpTree, item, checked, fieldKeys);
    }

    if (checkType === 'radio') {
      tmpTree = resetRadio(tmpTree, item, checked, fieldKeys);
    }

    return tmpTree;

    function resetRadio(tree, item, checked, fieldKeys) {
      let tmpTree = [...tree];

      tmpTree = tmpTree.map((treeItem) => {
        if (treeItem[fieldKeys.idKey] === item[fieldKeys.idKey]) {
          return {
            ...treeItem,
            [fieldKeys.checkedKey]: true,
          };
        } else {
          return {
            ...treeItem,
            [fieldKeys.checkedKey]: false,
          };
        }
      });

      return tmpTree;
    }

    function resetCheckbox(tree, item, checked, fieldKeys) {
      let tmpTree = [...tree];

      tmpTree = tmpTree.map((treeItem) => {
        if (treeItem[fieldKeys.idKey] === item[fieldKeys.idKey]) {
          return {
            ...treeItem,
            [fieldKeys.checkedKey]: checked,
          };
        } else {
          return treeItem;
        }
      });
      recursiveDesc(item);
      recursiveAsc(item);

      return tmpTree;

      /**
       * 广度优先遍历
       */
      function recursiveDesc(item) {
        const children = [];
        tmpTree = tmpTree.map((treeItem) => {
          if (treeItem[fieldKeys.pIdKey] === item[fieldKeys.idKey]) {
            children.push(treeItem);
            return {
              ...treeItem,
              [fieldKeys.checkedKey]: checked,
            };
          } else {
            return treeItem;
          }
        });

        children.forEach((childrenItem) => {
          recursiveDesc(childrenItem);
        });
      }

      function recursiveAsc(item) {
        const parentId = item[fieldKeys.pIdKey];
        if (!parentId) {
          return;
        }

        const siblings = tmpTree.filter((treeItem) => {
          return treeItem[fieldKeys.pIdKey] === parentId;
        });
        const allChecked = siblings.every((siblingItem) => {
          return siblingItem[fieldKeys.checkedKey];
        });

        let parentItem;

        tmpTree = tmpTree.map((treeItem) => {
          if (treeItem[fieldKeys.idKey] === parentId) {
            parentItem = {
              ...treeItem,
              // [fieldKeys.checkedKey]: allChecked
            };
            // 节点禁用时，不改变选中状态
            if (!parentItem[fieldKeys.disabledKey]) {
              parentItem[fieldKeys.checkedKey] = allChecked;
            }
            return parentItem;
          } else {
            return treeItem;
          }
        });

        recursiveAsc(parentItem);
      }
    }
  }

  constructor(props) {
    super(props);
    this.activeItem = null;
    this.fakeActiveItem = null;

    this.state = {
      hoverModel: {},
    };

    this.state = {
      ...this.state,
      ...this.init(props),
    };
  }

  componentWillReceiveProps(nextProps) {
    if (
      nextProps.value !== this.props.value ||
      nextProps.options !== this.props.options
    ) {
      this.setState({
        ...this.init(nextProps),
      });
    }
  }

  init(props) {
    const fieldKeys = this.getFieldKeys(props);
    const { options, optionStructureType, checkType } = props;
    const value = props.value || [];
    let tree = new Tree({
      type: optionStructureType,
      ...fieldKeys,
      data: options,
    }).getData();

    /**
     * 重置 checked 状态
     */
    value.forEach((valueItem) => {
      const target = tree.filter((treeItem) => {
        return treeItem[fieldKeys.idKey] === valueItem;
      })[0];
      if (!target) {
        console.log(`值${valueItem}在数据集合中不存在`);
        return;
      }
      tree = Cascader.resetChecked(tree, target, checkType, true, fieldKeys);
    });
    /**
     * 重置 active 状态
     */
    this.fakeActiveItem = null;
    if (!this.activeItem && value[0] != null) {
      this.fakeActiveItem = tree.filter((treeItem) => {
        return treeItem[fieldKeys.idKey] === value[0];
      })[0];
    }

    tree = this.resetActive(
      tree,
      this.activeItem || this.fakeActiveItem,
      fieldKeys,
    );
    const menu = this.getMenu(tree, fieldKeys);

    return {
      tree,
      menu,
    };
  }

  getMenu(tree, fieldKeys) {
    const menu = [
      tree.filter((treeItem) => {
        return treeItem[fieldKeys.pIdKey] == null;
      }),
    ];

    recursive(menu[0]);

    return menu;

    function recursive(list) {
      list.some((item) => {
        if (item[fieldKeys.activeKey]) {
          const tmpList = tree.filter((treeItem) => {
            return treeItem[fieldKeys.pIdKey] === item[fieldKeys.idKey];
          });

          if (tmpList && tmpList.length) {
            menu.push(tmpList);
            recursive(tmpList);
          }
          return true;
        }
        return false;
      });
    }
  }

  getFieldKeys(props) {
    props = props || this.props;
    const { fieldKeys } = props;

    return {
      labelKey: fieldKeys.labelKey || 'label',
      childrenKey: fieldKeys.childrenKey || 'children',
      idKey: fieldKeys.idKey || 'id',
      pIdKey: fieldKeys.pIdKey || 'pId',
      activeKey: fieldKeys.activeKey || 'active',
      checkedKey: fieldKeys.checkedKey || 'checked',
      disabledKey: fieldKeys.disabledKey || 'disabled',
    };
  }

  handleClick = (item, index) => {
    this.activeItem = item;
    this.props.onClick && this.props.onClick(item);

    const fieldKeys = this.getFieldKeys();
    const { tree } = this.state;

    if (item[fieldKeys.activeKey]) {
      return;
    }

    const tmpTree = this.resetActive(tree, item, fieldKeys, false);
    const menu = this.getMenu(tmpTree, fieldKeys);

    this.setState({
      tree: tmpTree,
      menu,
    });
  };

  resetActive(tree, activeItem, fieldKeys) {
    if (!activeItem) {
      return tree;
    }
    let tmpTree = [...tree];

    tmpTree = tmpTree.map((treeItem) => {
      return {
        ...treeItem,
        [fieldKeys.activeKey]: false,
      };
    });

    recursive(activeItem);

    return tmpTree;

    function recursive(activeItem) {
      tmpTree = tmpTree.map((treeItem) => {
        if (treeItem[fieldKeys.idKey] === activeItem[fieldKeys.idKey]) {
          return {
            ...treeItem,
            [fieldKeys.activeKey]: true,
          };
        } else {
          return treeItem;
        }
      });

      const parentItem = tmpTree.filter((treeItem) => {
        return treeItem[fieldKeys.idKey] === activeItem[fieldKeys.pIdKey];
      })[0];

      if (parentItem) {
        recursive(parentItem);
      }
    }
  }

  handleChange = (item, e) => {
    const { checkType } = this.props;
    const fieldKeys = this.getFieldKeys();

    if (item[fieldKeys.disabledKey]) {
      return;
    }

    if (!this.activeItem && this.fakeActiveItem) {
      this.activeItem = this.fakeActiveItem;
    }

    const tmpTree = Cascader.resetChecked(
      this.state.tree,
      item,
      checkType,
      e.target.checked,
      fieldKeys,
    );
    const { checkedValue, checkedResult } = Cascader.getCheckedInfo(
      tmpTree,
      checkType,
      fieldKeys,
    );

    this.props.onChange && this.props.onChange(checkedValue, checkedResult);

    this.setState({
      tree: tmpTree,
    });
  };

  // 渲染每一个选项
  renderItem(item, index) {
    const fieldKeys = this.getFieldKeys();
    const { tree } = this.state;

    const isLeafNode = this.props.isLeafNode
      ? this.props.isLeafNode(item)
      : !(
          item &&
          item[fieldKeys.childrenKey] &&
          item[fieldKeys.childrenKey].length
        );

    const active = item[fieldKeys.activeKey];

    // const checkEl = (
    //     <label
    //         style={{ cursor: 'pointer', paddingRight: '8px' }}
    //         onClick={(e) => {
    //             e.stopPropagation();
    //         }}>

    //         <input
    //             style={{ cursor: 'pointer' }}
    //             type={this.props.checkType}
    //             checked={item[fieldKeys.checkedKey] || false}
    //             disabled={item[fieldKeys.disabledKey] || false}
    //             onChange={this.handleChange.bind(this, item)}
    //         />
    //     </label>
    // );
    const checkElProps = {
      checked: item[fieldKeys.checkedKey] || false,
      disabled: item[fieldKeys.disabledKey] || false,
      onChange: this.handleChange.bind(this, item),
    };
    const checkEl = (
      <div
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        {this.props.checkType === 'checkbox' ? (
          <CheckBox {...checkElProps} />
        ) : (
          <Radio {...checkElProps} />
        )}
      </div>
    );

    const itemTextStyle = active ? { color: variables.activeFontColor } : {};

    return (
      <div
        key={index}
        onClick={this.handleClick.bind(this, item, index)}
        onMouseEnter={() => {
          this.setState({
            hoverModel: {
              [item[fieldKeys.idKey]]: true,
            },
          });
        }}
        onMouseLeave={() => {
          this.setState({
            hoverModel: {
              [item[fieldKeys.idKey]]: false,
            },
          });
        }}
      >
        {this.props.renderItem ? (
          this.props.renderItem(item, index)
        ) : (
          <div
            style={{
              ...styles.item,
              backgroundColor: this.state.hoverModel[item[fieldKeys.idKey]]
                ? variables.hoverBackgroundColor
                : active
                ? variables.activeBackgroundColor
                : variables.backgroundColor,
            }}
          >
            <div
              style={{
                ...styles.itemText,
                ...itemTextStyle,
              }}
            >
              {checkEl} <span>{item[fieldKeys.labelKey]}</span>
            </div>

            {!isLeafNode ? (
              this.props.renderIcon ? (
                this.props.renderIcon()
              ) : (
                <span style={{ marginLeft: '15px' }}>&gt;</span>
              )
            ) : null}
          </div>
        )}
      </div>
    );
  }

  renderMenuItem(menuItem, menuIndex, menu) {
    const style = {
      borderRightColor:
        menuIndex < menu.length - 1 ? variables.borderColor : 'transparent',
      borderRightWidth: '1px',
      borderRightStyle: 'solid',
    };

    return (
      <ul
        key={menuIndex}
        style={{
          ...styles.menuItem,
          ...style,
        }}
      >
        <li>
          {menuItem.map((item, index) => {
            return this.renderItem(item, index, menuIndex);
          })}
        </li>
      </ul>
    );
  }

  render() {
    const { options, style, fieldKeys } = this.props;
    const { tree, menu } = this.state;

    return (
      <div
        style={{
          ...styles.container,
          ...style,
        }}
      >
        {menu.map((item, index) => {
          return this.renderMenuItem(item, index, menu);
        })}
      </div>
    );
  }
}
