/* eslint-disable react/prop-types */
/* eslint-disable no-unused-expressions */
/* eslint-disable react/jsx-filename-extension */
/* eslint-disable no-empty */
import React from 'react';
import {
    Input,
} from 'antd';

function InputValidate(props) {
    const {
        defaultValue, onChange, disabled, placeholder
    } = props;
    let { valid } = props;
    const [innerValue, setInnerValue] = React.useState();
    if (!valid) {
        valid = (val) => {
            const reg = new RegExp('^[0-9]*$', 'g');
            if (val) {
                return reg.test(val);
            } else { // 可以为空
                return true;
            }
        };
    }
    React.useEffect(() => {
        setInnerValue(defaultValue || '');
    }, [defaultValue]);
    return (
        <Input
            disabled={disabled}
            value={innerValue}
            placeholder={placeholder}
            onChange={(e) => {
                const val = e.target.value;
                if (valid(val)) {
                    setInnerValue(val);
                    onChange && onChange(val);
                }
            }}
        />
    );
}

export default React.memo(InputValidate);
