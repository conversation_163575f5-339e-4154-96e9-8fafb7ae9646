/* eslint-disable react/prop-types */

import classNames from 'classnames';
import React from 'react';

import './index.scss';

const prefix = 'lead-detail';

// 驼峰字符串转 '-' 连接
const toCssStr = str =>
    str.replace(/\B([A-Z])/g, '-$1')
        .toLowerCase();

const cls = className => `${prefix}-${toCssStr(className)}`;


const getClasses = (props) => {
    const config = ['gray', 'relative', 'noPadding', 'flex', 'justifyCenter', 'flexColumn', 'alignCenter'];

    const res = {};

    config.forEach((el) => {
        res[cls(el)] = props[el];
    });
    return res;
};

// props: {children, gray, relative, style, className, noPadding, flex, justifyCenter}
const Wrapper = (props) => {
    const {
        className,
        style,
        children,
    } = props;
    const classes = classNames('lead-detail-wrapper', className || '', getClasses(props));
    return (
        <div className={classes} style={style || {}}>
            {children}
        </div>
    );
};

export default Wrapper;
