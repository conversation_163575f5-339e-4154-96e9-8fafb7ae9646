.roonext-cascade-multi {
  float: left;
  overflow: hidden;
  cursor: auto;
  border: 1px solid rgba(31,56,88,.2);
  background-color: #fff
}

.roonext-cascade-multi .roonext-cascade-multi-content {
  float: left;
  overflow-y: auto;
  width: 22%;
  height: 310px;
  padding: 14px 0;
  border-left: 1px solid #e8e8e8;
  background-color: #fff
}

.roonext-cascade-multi .roonext-cascade-multi-content .roonext-cascade-multi-list-item-active,.roonext-cascade-multi .roonext-cascade-multi-content .roonext-cascade-multi-list-item:hover {
  background-color: rgba(31,56,88,.1)
}

.roonext-cascade-multi .roonext-cascade-multi-content:first-child {
  border-left: 0
}

.roonext-cascade-multi .roonext-cascade-multi-content .roonext-cascade-multi-list-item {
  overflow: hidden;
  height: 28px;
  padding: 0 15px;
  white-space: nowrap;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  line-height: 28px;
  -o-text-overflow: ellipsis
}

.roonext-cascade-multi .roonext-cascade-multi-content .roonext-cascade-multi-list-item s {
  margin-right: 5px
}

.roonext-cascade-multi .roonext-cascade-multi-content .roonext-cascade-multi-list-item .roonext-cascade-multi-item-label span {
  vertical-align: middle
}

.roonext-cascade-multi .roonext-cascade-multi-content .roonext-cascade-multi-list-item .roonext-cascade-multi-item-disabled,.roonext-cascade-multi .roonext-cascade-multi-content .roonext-cascade-multi-list-item .roonext-cascade-multi-item-disabled * {
  cursor: not-allowed
}

.roonext-cascade-multi .roonextn x er-cascade-multi-content .roonextnext-cascade-multi-list-noData {
  display: block;
  margin-top: 7px;
  margin-left: 21px
}

.roonext-cascade-multi .roonext-cascade-multi-result {
  float: left;
  width: 32%;
  height: 310px;
  padding: 0;
  border-left: 1px solid #e8e8e8;
  background-color: #fff
}

.roonext-cascade-multi .roonext-cascade-multi-result .roonext-cascade-multi-result-title {
  margin: 20px 15px 10px;
  padding: 0;
  color: #76889A
}

.roonext-cascade-multi .roonext-cascade-multi-result .roonext-cascade-multi-result-title .roonext-cascade-multi-result-clean {
  float: right;
  margin-right: 6px;
  cursor: pointer;
  color: #3C99D8
}

.roonext-cascade-multi .roonext-cascade-multi-result .roonext-cascade-multi-result-tree {
  overflow-x: hidden;
  overflow-y: auto;
  height: 250px;
  margin: 0
}

.roonext-cascade-multi .roonext-cascade-multi-result .roonext-cascade-multi-result-tree>ul {
  width: 220px
}

.roonext-cascade-multi .roonext-cascade-multi-result .roonext-cascade-multi-result-tree ul li {
  white-space: nowrap;
  line-height: 25px
}

.roonext-cascade-multi .roonext-cascade-multi-result .roonext-cascade-multi-result-tree ul li .roonext-icon-triangle-down,.roonext-cascade-multi .roonext-cascade-multi-result .roonext-cascade-multi-result-tree ul li .roonext-icon-triangle-right {
  margin-right: 5px;
  vertical-align: middle;
  color: #999
}

.roonext-cascade-multi .roonext-cascade-multi-result .roonext-cascade-multi-result-tree ul li .tree-node-ul-li-div:hover {
  background-color: rgba(31,56,88,.04)
}

.roonext-cascade-multi .roonext-cascade-multi-result .roonext-cascade-multi-result-tree ul li .tree-node-ul-li-div .tree-node-ul-li-span {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  background-color: transparent
}

.roonext-cascade-multi .roonext-cascade-multi-result .roonext-cascade-multi-result-tree ul li .tree-node-ul-li-div .tree-node-ul-li-span:hover {
  color: rgba(0,0,0,.6);
  background-color: transparent
}

.roonext-cascade-multi .roonext-cascade-multi-result .roonext-cascade-multi-result-tree ul li .tree-node-ul-li-div .tree-node-ul-li-span:hover .tree-node-ul-li-del {
  transform: scale(1);
  opacity: 1
}

.roonext-cascade-multi .roonext-cascade-multi-result .roonext-cascade-multi-result-tree ul li .tree-node-ul-li-div .tree-node-ul-li-span-label {
  display: inline-block;
  overflow-x: hidden;
  vertical-align: middle;
  white-space: nowrap;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis
}

.roonext-cascade-multi .roonext-cascade-multi-result .roonext-cascade-multi-result-tree ul li .tree-node-ul-li-div .tree-node-ul-li-span .tree-node-ul-li-all {
  margin-left: 10px;
  vertical-align: middle;
  color: #999
}

.roonext-cascade-multi .roonext-cascade-multi-result .roonext-cascade-multi-result-tree ul li .tree-node-ul-li-div .tree-node-ul-li-span .tree-node-ul-li-del {
  position: absolute;
  right: 42px;
  cursor: pointer;
  transition: none;
  transform: scale(0);
  color: #3C99D8
}

.roonext-cascade-multi-select-panel-content {
  position: relative;
  box-shadow: 0 1px 4px 0 rgba(31,56,88,.15);
  border-radius: 6px
}

.roonext-cascade-multi-select-panel-content .roonext-cascade-multi {
  border-radius: 6px 6px 0 0;
  overflow: hidden
}

.roonext-cascade-multi-select-panel-content .roonext-cascade-multi-select-footer {
  border-radius: 0 0 6px 6px;
  overflow: hidden;
  clear: both
}

.roonext-cascade-multi-select-panel-content .roonext-cascade-multi-select-panel-wrap {
  border-radius: 6px;
  box-shadow: 0 1px 4px 0 rgba(31,56,88,.15);
  zoom:1;overflow: hidden
}

.roonext-cascade-multi-input {
  background-color: #fff
}

.roonext-cascade-multi-input::-ms-clear {
  display: none
}

.roonext-cascade-multi-large {
  height: 36px
}

.roonext-cascade-multi-middle {
  min-height: 32px;
  line-height: 32px
}

.roonext-cascade-multi-small {
  min-height: 28px;
  line-height: 28px
}

.roonext-cascade-multi-text-result {
  overflow-x: hidden;
  margin-right: 30px;
  white-space: nowrap;
  text-overflow: ellipsis
}

.roonext-cascade-multi-text-result-input {
  width: 100%;
  padding-right: 10px;
  border: 0;
  background-color: transparent
}

.roonext-cascade-multi-text-result-input::-ms-clear {
  display: none
}

.roonext-cascade-multi-select-footer {
  float: left;
  height: 52px;
  text-align: center;
  border-width: 0 1px 1px;
  border-style: solid;
  border-color: rgba(31,56,88,.2);
  background-color: #fff;
  line-height: 52px
}

.roonext-cascade-multi.ucms-panel {
  border-radius: 6px
}

.roonext-cascade-multi-model .roonext-cascade-multi {
  border-left: none;
  border-right: none
}

.roonext-cascade-multi-model-result {
  margin: 15px 0
}

.roonext-cascade-multi-model-result-ul {
  overflow: hidden
}

.roonext-cascade-multi-model-result-ul-list {
  position: relative;
  float: left;
  margin-right: 8px;
  margin-bottom: 8px;
  padding: 6px 20px;
  border-radius: 2px;
  background-color: #f0f0f0
}

.roonext-cascade-multi-model-result-ul-list-content {
  transition: margin .3s cubic-bezier(.165,.84,.44,1);
  transform: scale(1);
  margin-right: 10px;
  margin-left: -10px
}

.roonext-cascade-multi-model-result-ul-list-remove {
  position: absolute;
  top: 3px;
  right: 5px;
  cursor: pointer;
  transform: scale(1);
  vertical-align: middle;
  opacity: 1;
  color: rgba(31,56,88,.4)
}

.roonext-cascade-multi-model-result-ul-list-remove:before {
  content: '\e610'
}

.roonext-cascade-multi-model-result-ul-list-remove:hover {
  color: rgba(31,56,88,.6)
}

.roonext-cascade-multi-model-expand {
  cursor: pointer;
  color: #ff6f00
}

.roonext-cascade-multi-model .roonext-dlg-body {
  overflow: hidden;
  padding: 0
}

.use-svg .roonext-tree-checkbox {
  position: relative;
  display: inline-block;
  margin-right: 10px;
  width: 16px;
  height: 16px;
  border: 1px solid #BABCCC;
  background-color: #fff;
  vertical-align: sub;
  border-radius: 2px;
}
.use-svg .roonext-tree-checkbox:hover{
    border: 1px solid #9799a7;
}
.use-svg .roonext-tree-checkbox.roonext-tree-checkbox-checked {
    background-color: #00abe4;
    border-color: #00abe4 !important;
}
.use-svg .roonext-tree-checkbox.roonext-tree-checkbox-checked::before {
  position: absolute;
  display: inline-block;
  content: '';
  left: 50%;
  top: 30%;
  -webkit-transform: translate(-50%, -30%) rotate(45deg);
  -ms-transform: translate(-50%, -30%) rotate(45deg);
  transform: translate(-50%, -30%) rotate(45deg);
  width: 6px;
  height: 10px;
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
}

.roonext-cascader-wrapper {
  min-height: 36px;
  width: 100%;
  position: relative;
  color: #333;
  border-radius: 3px;
  background-color: #fff;
  transition: all .3s
}
.kuma-cascade-multi-large {
  height: 36px
}

.roonext-cascader-arrow,.roonext-cascader-close-wrap {
  position: absolute;
  top: 50%;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  transition: all .3s;
  text-align: center
}

.roonext-cascader-wrapper:hover {
  border-color: #a5afbc
}

.roonext-cascader-focus,.roonext-cascader-focus:hover {
  border-color: #79889b
}

.roonext-cascader-wrapper:hover .roonext-icon {
  color: #a5afbc
}

.roonext-cascader-clearable:hover .roonext-cascader-close-wrap {
  visibility: visible;
  opacity: 1;
  transform: scale(1)
}

.roonext-cascader-close-wrap {
  visibility: hidden;
  opacity: 0;
  transform: scale(.6);
  right: 20px;
  line-height: 20px;
  cursor: pointer
}

.roonext-cascader-close-wrap .roonext-icon {
  color: #a5afbc;
  font-size: 14px
}

.roonext-cascader-placeholder {
  color:#b9bdcc;
  text-align: left;
}
.roonext-dropdown-normal {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height:36px;
  padding:0 10px;
  border-color:#E9EAF2 !important;
}
.roonext-dropdown-popup{
  height:auto;
  max-height: none;
  border:none;
  box-shadow: none;
  background:none;
}

/*
  重写roonext dropdown样式
*/
.roo-btn .roonext-icon {
  margin-right:0;
}
.roo-icon-arrow-down:before {
  color:#c2c4d2;
  content: "\EA0B";
}

.roo-icon-arrow-up:before {
  color:#c2c4d2;
  content: "\EA0B";
}