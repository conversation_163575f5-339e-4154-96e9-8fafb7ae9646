/* eslint-disable react/prop-types */
/* eslint-disable no-unused-expressions */
import React, { useState, useEffect, useRef } from 'react';
import { Selector } from '@roo/roo';
import getPrefix from '../../common/service/getPrefix';
import axios, { CancelTokenSource } from 'axios';
import { useForm } from 'antd/es/form/Form';

const { CancelToken } = axios;

function SelectUser(props) {
    const { onChange, limitUser, clear, bindValue } = props;
    const [user, setUser] = useState();
    const [options, setOptions] = useState();
    const source = useRef(CancelTokenSource);
    useEffect(() => {
        if (limitUser) {
            const u = {
                uid: limitUser.id,
                mis: limitUser.login,
                name: limitUser.name,
            };
            setOptions([u]);
        }
    }, [limitUser]);

    useEffect(() => {
        if (bindValue) {
            setOptions([bindValue]);
            setUser(bindValue);
        }
    }, [bindValue]);

    useEffect(() => {
        if (clear) {
            setOptions([]);
            setUser('');
        }
    }, [clear]);

    return (
        <div>
            <Selector
                value={user}
                options={options}
                filterable
                filterMethod={(v, opt, raw) =>
                    raw.name.includes(v) || raw.mis.includes(v)
                }
                rawOption
                fieldNames={{
                    label: 'name',
                    value: 'uid',
                }}
                optionRenderer={(option, raw) => {
                    const { name, mis } = raw;
                    return `${name} ${mis}`;
                }}
                contentRenderer={raw => {
                    if (raw) {
                        return `${raw.name}(${raw.mis})`;
                    } else {
                        return null;
                    }
                }}
                clearable
                onChange={e => {
                    setUser(e);
                    onChange && onChange(e ? e.uid : '');
                }}
                onSearch={key => {
                    if (limitUser) {
                        return;
                    }
                    if (source.current) {
                        source.current.cancel();
                        source.current = null;
                    }
                    source.current = CancelToken.source();
                    const cancelToken = source.token;

                    axios
                        .get(
                            `${getPrefix(
                                '/highsea/common/searchUser',
                            )}/highsea/common/searchUser?key=${key}`,
                            {
                                cancelToken,
                            },
                        )
                        .then(res => {
                            const { data } = res.data;
                            setOptions(data);
                        });
                }}
                // notFoundContent={
                //     <p style={{ margin: '10px' }}>加载中...</p>
                // }
                placeholder="输入mis或姓名搜索"
            />
        </div>
    );
}

export default SelectUser;
