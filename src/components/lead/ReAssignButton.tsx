import { Form, message, Modal, Select, Typography } from 'antd';
import React, { useState } from 'react';
import { NewSignTargetStatus } from '../../types';
import SelectUser from '../selectUser';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { Button } from '@roo/roo';

export interface ReAssignButtonProps {
    ids: string[];
    onReload: () => void;
}

const statusListOptions = [
    {
        label: '待首次跟进',
        value: NewSignTargetStatus.WAIT_FIRST_FOLLOW,
    },
    {
        label: '待二次跟进',
        value: NewSignTargetStatus.WATI_SECOND_FOLLOW,
    },
    {
        label: '待三次跟进',
        value: NewSignTargetStatus.WATI_THIRD_FOLLOW,
    },
    {
        label: '已闭环（成功闭环）',
        value: NewSignTargetStatus.SUCCESS_CLOSED_LOOP,
    },
    {
        label: '已闭环（失败闭环）',
        value: NewSignTargetStatus.FAILURE_CLOSED_LOOP,
    },
];

const ReAssignButton = (props: ReAssignButtonProps) => {
    const [open, setOpen] = useState(false);
    const [form] = Form.useForm();

    const onClickBtn = () => {
        if (!props.ids.length) {
            message.error('请先选择线索');
            return;
        }
        setOpen(x => !x);
    };

    const onReAssign = async () => {
        try {
            const data = await form.validateFields();

            const res = await apiCaller.send(
                '/api/newSign/manage/changeOwner',
                {
                    leadIdList: props.ids,
                    ...data,
                },
            );

            if (res.code !== 0) {
                return;
            }

            message.success('变更认领人成功');
            setOpen(false);
            props.onReload();
        } catch (e) {}
    };

    return (
        <>
            <Button className="leadList__button" onClick={onClickBtn}>
                变更认领人
            </Button>
            <Modal
                open={open}
                title="变更认领人"
                onOk={onReAssign}
                onCancel={() => setOpen(false)}
            >
                <Typography.Text type="secondary">
                    已选择{props.ids?.length}条线索
                </Typography.Text>

                <Form form={form} layout="vertical">
                    <Form.Item
                        label="新的认领人"
                        name="newOwnerUid"
                        rules={[{ required: true }]}
                    >
                        <SelectUser />
                    </Form.Item>

                    <Form.Item
                        label="指定线索状态"
                        name="targetStatus"
                        rules={[{ required: true }]}
                    >
                        <Select placeholder="请选择">
                            {statusListOptions.map(item => (
                                <Select.Option
                                    key={item.value}
                                    value={item.value}
                                >
                                    {item.label}
                                </Select.Option>
                            ))}
                        </Select>
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
};

export default ReAssignButton;
