import { message, Modal } from 'antd';
import React from 'react';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { Button } from '@roo/roo';

export interface BatchCleanButtonProps {
    ids: string[];
    onReload: () => void;
}

const BatchCleanButton = (props: BatchCleanButtonProps) => {
    const [modal, contextHolder] = Modal.useModal();
    const onClickBtn = async () => {
        if (!props.ids.length) {
            message.error('请先选择线索');
            return;
        }

        const confirm = await modal.confirm({
            title: '确认清理这些线索？',
            content: `已勾选${props.ids.length}条线索，线索清理后将无法再跟进，操作不可撤回，请谨慎操作。确认后toast展示成功清理条数与清理失败条数。`,
        });

        if (!confirm) {
            return;
        }

        const res = await apiCaller.send('/api/newSign/manage/delete', {
            leadIdList: props.ids,
        });

        if (res.code !== 0) {
            return;
        }

        message.success(res.data || '线索清理成功');
        props.onReload();
    };

    return (
        <>
            <Button className="leadList__button" onClick={onClickBtn}>
                批量清理
            </Button>
            {contextHolder}
        </>
    );
};

export default BatchCleanButton;
