import React from 'react';
import OrganizationSelector from '@roo/roo-plus/OrganizationSelector';

export function OrganizationSelectorWrap(props) {
    const { value, onChange } = props;
    return (
        <OrganizationSelector
            value={value}
            placeholder="组织结构选择"
            pidUrl="/xianfu/api/clue/panda/uicomponent/api/orgs/getByPid"
            searchUrl="/xianfu/api/clue/panda/uicomponent/api/orgs/search"
            params={{
                sources: 1,
                parentId: 0,
                backtrackOrgType: 2,
            }}
            onConfirm={(v) => {
                if (onChange) {
                    onChange(v);
                }
            }}
            multiple
        />
    );
}

export default OrganizationSelectorWrap;
