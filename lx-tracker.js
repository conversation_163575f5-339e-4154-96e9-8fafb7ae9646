/* eslint-disable no-console */
import { EventTracker } from '@mfe/lx-event-tracker-sdk';

export function registerTracker() {
    try {
        if (!window.LXCurrTracker) {
            const tracker = new EventTracker(config);
            window.LXCurrTracker = tracker;
            tracker.listen(document.querySelector('#root'));
        }
    } catch (e) {
        console.error('lx-event-tracker-sdk error', e);
    }
}

const config = {
    defaultCategory: 'waimai_m',
    channelId: 222,
    appName: 'waimai_xianfu',
    jumpLinkTemplate: 'https://ua.sankuai.com/main/#url=/#/ocean/analysis?channelId={channelId}&id={id}&type={type}',
    pages: {
        LX_DEFAULT_PAGE: {
            cid: 'c_waimai_m_nhqq7174',
            uaId: 42469124,
            pageName: '未接埋点页面',
            modules: {},
        },
        leadList: {
            cid: 'c_waimai_m_crss63af',
            uaId: 42385775,
            modules: {
                查询按钮: {
                    uaId: 351062,
                    bid: 'b_waimai_m_1plctamc_mc',
                    type: 'moduleClick',
                    lab: null,
                    options: {},
                },
            },
        },
        leadListMine: {
            cid: 'c_waimai_m_hqox9p0s',
            uaId: 42385776,
        },
        leadDetail: {
            "cid": "c_waimai_m_a491w7dq",
            "uaId": 43288563,
            "pageName": "详情页面",
            "modules": {}
        }
    },
};

export default {};
