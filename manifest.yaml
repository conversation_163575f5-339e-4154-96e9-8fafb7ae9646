version: v1
build:
  os: centos7
  tools:
    gcc-c++:
    node: 14.4.0
    git:
    make:
  deps:
    - npm-install-g: yarn
  run:
    workDir:
    cmd:
      - echo 'start build'
      - bash ./pre-deploy
  target:
    distDir: ./build/pages
    files:
      - ./
  cache:
    dirs:
      - node_modules
deploy:
  tools:
    node: 14.4.0
  targetDir: /opt/meituan/www/waimai_mfe_tech_igate/public/static/igate/clue
